
[v-cloak] {
    display: none;
}
/* 标题 */
.first-title {position: relative;
  width: 100%;
  height: 95px;
  background-image: url('@/pages/qyzf/img/first-title.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  padding-left: 100px;
  padding-right: 50px;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: 20px 0;
  line-height: 110px;
  font-size: 45px;
  color: #dcefff;
  letter-spacing: 2px;
  font-weight: 700;
}
.first-title>i{
  position: absolute;
  width: 1062px;height: 90px;
  display: inline-block;
  background: url('@/pages/qyzf/img/line.png');
  background-size: 100%;
  animation-name: animateLine;
  animation-duration: 10s;
  animation-iteration-count:infinite;
  /* animation-direction:alternate; */
  /* animation-delay */
}
@keyframes animateLine{
  0%   {left: -20%;opacity: 0.4; }	
  50%   {opacity: 1; }	
  100% {left: 70%;opacity: 0.2; }
}
.second-title{
  width: 100%;
  height: 90px;
  font-size: 40px; display: flex;
  align-items: center;justify-content: center;
  color: #dcefff;
  background-image: url('@/pages/qyzf/img/second-title.png');
  background-size: 100% 100%;
  /* background: linear-gradient( 268deg, rgba(0,121,227,0) 0%, rgba(28,148,249,0.5882) 51%, rgba(8,17,26,0) 100%); */
  border-radius: 0px 0px 0px 0px;
  margin-bottom:20px;
  position: relative;
}
/* 鼠标禁用事件 */
.mouse-no{
  pointer-events: none;
}
.mouse-pointer{
  cursor: pointer;
}
.mouse-not{
  /* cursor: not-allowed; */
  cursor: default;
}
/* 效果 */
.red-color {
    background-image: linear-gradient(#ff6363 10%, #ffffff 60%, #ff7777be 10%) !important;
    -webkit-background-clip: text;
    color: transparent !important;
}
.yel-color {
    background-image: linear-gradient(#ffd79b 10%, #ffffff 60%, #ffc559 10%) !important;
    -webkit-background-clip: text;
    color: transparent !important;
}

.blue-color {
    background-image: linear-gradient(#caffff 10%, #ffffff 60%, #00c0ff 10%) !important;
    -webkit-background-clip: text;
    color: transparent !important;
}
.orange-color {
    background-image: linear-gradient(#FFE2CD 10%, #ffffff 60%, #FD852E 10%) !important;
    -webkit-background-clip: text;
    color: transparent !important;
}
.green-color {
    background-image: linear-gradient(#F0FFD7  10%, #ffffff 60%, #A9DB52 10%) !important;
    -webkit-background-clip: text;
    color: transparent !important;
}
/* 表格 */
.table {
    width: 100%;
}

.table-th {
    width: 100%;
    height: 60px;
    background-color: #00396f;
    margin-bottom: 10px;
    display: flex;
    align-items: center;
}

.th {
    flex: 0.3;
    font-size: 32px;
    /* text-align: center; */
    color: #77b3f1;
    margin-left: 10px;
}

.table-tr {
    width: 100%;
    height: 288px;
    overflow-y: auto;
}

.table-tr::-webkit-scrollbar {
    /*滚动条整体样式*/
    width: 4px;
    /*高宽分别对应横竖滚动条的尺寸*/
    height: 1px;
    /* scrollbar-arrow-color: red; */

}

.table-tr::-webkit-scrollbar-thumb {
    border-radius: 4px;
    /* -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2); */
    background: #20aeff;
    height: 8px;

}

.tr {
    width: 100%;
    padding: 10px 0;
    background-color: #0f2b4d;
    display: flex;
    align-items: center;
    margin-bottom: 10px;
}

.td {
    flex: 0.3;
    font-size: 32px;
    color: #fff;
    margin-left: 10px;
    /* text-align: center; */
}
.td>span:last-child{
    margin-left: 10px;
}

/* 下拉框 */

.select {
  display: inline-block;
  width: 300px;
  height: 55px;
  text-align: right;
  position: absolute;
  right: 120px;
  top: 0;
  z-index: 100;
}

.flow-icon {
  width: 25px;
  position: absolute;
  top: 20px;
  right: 14px;
}

.flow-icon>img {
  width: 25px;
}

.ul>div {
  width: 100%;
  height: 60px;
  line-height: 60px;
}



.ul {
  width: 100%;
  height: 60px;
  text-align: center;
  font-size: 32px;
  color: #fefefe;
  background-color: #132c4e;
  border: 1px solid #359cf8;
  border-radius: 40px;
  margin-top: 25px;
}
.ul>ul {
    overflow-y: auto;
    display: none;
  }
  .ul ul>li {
  width: 100%;
  height: 62px;
  line-height: 62px;
  background-color: #132c4ec2;
  padding-right: 20px;
  box-sizing: border-box;
}

.ul ul>li:hover {
  background-color: #359cf8;
}

.ul-active {
  display: block !important;
}

.ul>ul>li:first-of-type {
  border-radius: 40px 40px 0 0;
}

.ul ul::-webkit-scrollbar {
  /*滚动条整体样式*/
  width: 6px;
  /*高宽分别对应横竖滚动条的尺寸*/
  height: 1px;
  /* scrollbar-arrow-color: red; */

}

.ul ul::-webkit-scrollbar-thumb {
  border-radius: 6px;
  /* -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2); */
  background: #20aeff;
  height: 8px;

}
.s-flex {
  display: flex;
  flex-direction: row;
  align-items: center;
}