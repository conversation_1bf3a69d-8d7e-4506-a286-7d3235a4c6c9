<template>
  <div class="chart-item">
    <SubTitle title="人口流动趋势" />
    <div class="chart-container">
      <div id="mobilityTrend" class="chart"></div>
    </div>
  </div>
</template>

<script>
import SubTitle from '@/components/SubTitle.vue'
import { getCsdnInterface1 } from '@/api/csdnIndexApi'

export default {
  name: 'MobilityTrend',
  components: { SubTitle },
  data() {
    return {
      chartData: [],
      loading: false,
    }
  },
  mounted() {
    this.fetchData()
  },
  methods: {
    async fetchData() {
      try {
        this.loading = true
        // 获取 SubTitle 的 title 属性作为 type 参数
        const titleType = '流动趋势'
        const response = await getCsdnInterface1('rkhl_test', { type: titleType })

        console.log('接口返回数据:', response.data)

        // 处理接口返回的数据
        if (response.data && response.data.successful && response.data.data) {
          this.chartData = this.processChartData(response.data.data)
        } else {
          // 如果接口返回空数据或失败，使用空数组
          this.chartData = []
        }
      } catch (error) {
        console.error('获取数据失败:', error)
        // 请求失败时使用空数组
        this.chartData = []
      } finally {
        this.loading = false
        this.initChart()
      }
    },

    processChartData(data) {
      // 根据实际接口返回的数据结构进行处理
      // data 结构: { name: 年份, value: 流动人数, value1: 净迁移率 }
      if (Array.isArray(data) && data.length > 0) {
        return data.map((item) => ({
          name: item.name, // 年份
          value: parseInt(item.value) || 0, // 流动人数（柱状图）
          value1: parseFloat(item.value1) || 0, // 变化率（折线图）
        }))
      }

      // 如果数据格式不符合预期，返回默认数据
      const years = ['2020', '2021', '2022', '2023', '2024']
      const defaultFlowData = [-10325, -9293, -5879, -6661, -7736]
      const defaultRateData = [-1.70, -1.54, -0.98, 1.12, -1.32]
      return years.map((year, index) => ({
        name: year,
        value: defaultFlowData[index] || 0,
        value1: defaultRateData[index] || 0,
      }))
    },

    initChart() {
      const chart = this.$echarts.init(document.getElementById('mobilityTrend'))

      const categories = this.chartData.map((item) => item.name) // 年份
      const flowValues = this.chartData.map((item) => item.value) // 流动人数
      const rateValues = this.chartData.map((item) => item.value1) // 净迁移率

      // 计算两个y轴的范围,确保0刻度对齐
      const flowMax = Math.max(...flowValues, 0)
      const flowMin = Math.min(...flowValues, 0)
      const rateMax = Math.max(...rateValues, 0)
      const rateMin = Math.min(...rateValues, 0)

      // 计算比例,使0刻度对齐
      const flowRange = Math.max(Math.abs(flowMax), Math.abs(flowMin))
      const rateRange = Math.max(Math.abs(rateMax), Math.abs(rateMin))

      const option = {
        tooltip: {
          trigger: 'axis',
          backgroundColor: 'rgba(0, 0, 0, 0.8)',
          borderColor: '#0091FF',
          textStyle: { color: '#fff', fontSize: 26 },
          formatter: function (params) {
            let result = params[0].name + '<br/>'
            params.forEach((param) => {
              if (param.seriesName === '流动人数') {
                result += param.marker + param.seriesName + ': ' + param.value + '人<br/>'
              } else if (param.seriesName === '净迁移率') {
                result += param.marker + param.seriesName + ': ' + param.value + '%<br/>'
              }
            })
            return result
          },
        },
        legend: {
          data: ['流动人数', '净迁移率'],
          textStyle: { color: '#fff', fontSize: 24 },
          top: '5%',
        },
        grid: { left: '18%', right: '12%', top: '20%', bottom: '12%' },
        xAxis: {
          type: 'category',
          data: categories,
          axisLabel: { color: '#fff', fontSize: 24 },
          axisLine: { lineStyle: { color: '#333' } },
        },
        yAxis: [
          {
            type: 'value',
            name: '流动人数(人)',
            nameTextStyle: {
              color: '#fff',
              fontSize: 24,
              padding: [0, 0, 0, 10],
            },
            min: -flowRange,
            max: flowRange,
            axisLabel: { color: '#fff', fontSize: 24 },
            axisLine: { lineStyle: { color: '#333' } },
            splitLine: { lineStyle: { color: '#333' } },
          },
          {
            type: 'value',
            name: '净迁移率(%)',
            nameTextStyle: {
              color: '#fff',
              fontSize: 24,
              padding: [0, 10, 0, 0],
            },
            min: -rateRange,
            max: rateRange,
            axisLabel: { color: '#fff', fontSize: 24 },
            axisLine: { lineStyle: { color: '#333' } },
            splitLine: { show: false },
          },
        ],
        series: [
          {
            name: '流动人数',
            type: 'bar',
            yAxisIndex: 0,
            data: flowValues,
            itemStyle: {
              color: '#0091FF',
              borderRadius: [4, 4, 0, 0],
            },
            barWidth: 24,
            animationDuration: 1000,
            animationEasing: 'cubicOut',
          },
          {
            name: '净迁移率',
            type: 'line',
            yAxisIndex: 1,
            data: rateValues,
            lineStyle: {
              color: '#FF6B35',
              width: 3,
            },
            itemStyle: {
              color: '#FF6B35',
            },
            symbol: 'circle',
            symbolSize: 8,
            animationDuration: 1000,
            animationEasing: 'cubicOut',
          },
        ],
      }
      chart.setOption(option)

      // 响应式处理
      window.addEventListener('resize', () => {
        chart.resize()
      })
    },
  },
}
</script>

<style lang="less" scoped>
.chart-item {
  flex: 1;
  background: transparent;
  border-radius: 8px;
  padding: 10px;
  border: none;

  .chart-container {
    position: relative;

    .chart {
      width: 100%;
      height: 450px;
    }
  }
}
</style>

