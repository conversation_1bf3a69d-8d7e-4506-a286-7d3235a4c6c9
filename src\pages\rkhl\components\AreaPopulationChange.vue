<template>
  <div class="chart-item large">
    <SubTitle title="今日24小时总人数变化情况" />
    <div class="chart-container">
      <div class="hour-container">
        <div id="lineEcharts" class="chart"></div>
      </div>
    </div>
  </div>
</template>

<script>
import SubTitle from '@/components/SubTitle.vue'
import { getCsdnInterface1 } from '@/api/csdnIndexApi'

export default {
  name: 'AreaPopulationChange',
  components: {
    SubTitle,
  },
  props: {
    selectedArea: {
      type: String,
      default: '安陆市东大时代广场',
    },
  },
  data() {
    return {}
  },
  mounted() {
    // 初始化默认显示时维度
    this.getHourData()
  },
  watch: {
    selectedArea: {
      handler(newArea) {
        console.log('AreaPopulationChange 接收到区域变化:', newArea)
        // 当区域变化时重新请求数据
        this.getHourData()
      },
      immediate: false, // 不立即执行,避免重复请求
    },
  },
  methods: {
    // 获取小时数据
    async getHourData() {
      try {
        const res = await getCsdnInterface1('qyhx_pop_rkbhqk', {
          name: this.selectedArea, // 传递选中的区域名称
        })
        this.xyLineFun('lineEcharts', res.data.data)
      } catch (error) {
        console.error('获取小时数据失败:', error)
        this.renderMockHourData()
      }
    },

    renderMockHourData() {
      // 模拟小时数据渲染
      const mockData = Array.from({ length: 24 }, (_, i) => ({
        time: i.toString().padStart(2, '0') + ':00',
        value: Math.floor(Math.random() * 1000) + 500,
      }))
      this.xyLineFun('lineEcharts', mockData, 'data')
    },

    // 小时数据线图
    xyLineFun(domId, data) {
      console.log('1111', data)
      const chartDom = document.getElementById(domId)
      if (!chartDom) return

      this.$echarts.dispose(chartDom)
      const chart = this.$echarts.init(chartDom)

      const xData = data.map((item) => item.time_label)
      const yData = data.map((item) => item.hourly_count)

      const option = {
        grid: {
          left: '5%',
          right: '5%',
          top: '10%',
          bottom: '10%',
          containLabel: true,
        },
        tooltip: {
          show: true,
          trigger: 'axis',
          textStyle: {
            fontSize: 22,
            color: '#fff',
          },
          backgroundColor: 'rgba(51, 51, 51, 0.7)',
          borderWidth: 0,
        },
        xAxis: {
          type: 'category',
          data: xData,
          axisLabel: { color: '#fff', fontSize: 24 },
          axisLine: { lineStyle: { color: '#fff' } },
        },
        yAxis: {
          type: 'value',
          axisLabel: { color: '#fff', fontSize: 24 },
          axisLine: { lineStyle: { color: '#fff' } },
          splitLine: { lineStyle: { color: 'rgba(255, 255, 255, 0.1)' } },
        },
        series: [
          {
            type: 'line',
            data: yData,
            smooth: true,
            lineStyle: { color: '#00c0ff', width: 2 },
            itemStyle: { color: '#00c0ff' },
            areaStyle: {
              color: new this.$echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: 'rgba(0, 192, 255, 0.3)' },
                { offset: 1, color: 'rgba(0, 192, 255, 0)' },
              ]),
            },
          },
        ],
      }

      chart.setOption(option)
    },
  },
}
</script>

<style lang="less" scoped>
.chart-item {
  flex: 1;
  background: transparent;
  border-radius: 8px;
  padding: 10px;
  border: none;

  &.large {
    flex: 1.5;
  }

  .chart-container {
    position: relative;

    .time-controls {
      position: absolute;
      top: 10px;
      right: 10px;
      z-index: 10;
    }

    .hour-container {
      position: relative;

      .input_box {
        cursor: pointer;
        width: 200px;
        height: 35px;
        border-radius: 20px;
        color: #fff;
        background-color: #132c4ed0;
        border: 1px solid #359cf8;
        position: absolute;
        right: 20px;
        top: 50px;
        text-align: center;
        z-index: 888;
        font-size: 16px;
      }

      .timePicker {
        font-size: 14px;
        position: absolute;
        right: 10px;
        top: 90px;
        list-style: none;
        width: 310px;
        display: flex;
        color: #ccc;
        flex-wrap: wrap;
        background-color: #132c4eab;
        border: 1px solid #359cf8;
        z-index: 888;
        padding: 10px;
        border-radius: 8px;

        li {
          width: max-content;
          padding: 4px 8px;
          margin: 2px;
          border: 1px solid transparent;
          cursor: pointer;
          border-radius: 4px;
          transition: all 0.3s ease;

          &.timePicker_active {
            border: 1px solid #fff;
            color: #359cf8;
            background: rgba(53, 156, 248, 0.2);
          }

          &:hover {
            border: 1px solid #359cf8;
            color: #fff;
          }
        }
      }
    }

    .chart {
      width: 100%;
      height: 400px;
    }
  }
}
</style>
