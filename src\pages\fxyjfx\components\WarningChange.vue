<!--
 * @Description: 预警详情弹框
 * @Version: 1.0
 * @Autor: wjb
 * @Date: 2025-09-25 16:00:00
 * @LastEditors: wjb
 * @LastEditTime: 2025-09-26 09:22:33
-->
<template>
  <div class="change-content">
    <div class="form-container">
      <!-- 事件基本信息 -->
      <div class="form-section">
        <div class="section-header">事件基本信息</div>
        <div class="form-row">
          <div class="form-item">
            <label class="form-label">事件名称</label>
            <el-input v-model="formData.mc" placeholder="事件名称" class="form-input"></el-input>
          </div>
        </div>
        <div class="form-row">
          <div class="form-item full-width">
            <label class="form-label">基本情况</label>
            <el-input v-model="formData.ms" placeholder="事件描述" class="form-input"></el-input>
          </div>
        </div>
        <div class="form-row">
          <div class="form-item full-width">
            <label class="form-label">事件地点</label>
            <el-input v-model="formData.sjdd" placeholder="事件地点" class="form-input"></el-input>
          </div>
        </div>
      </div>

      <!-- 事件详情 -->
      <div class="form-section">
        <div class="section-header">事件详情</div>
        <div class="form-row">
          <div class="form-item">
            <label class="form-label">事件来源</label>
            <el-select v-model="formData.sjly" placeholder="请选择事件来源" class="form-select">
              <el-option label="人口活力" value="人口活力"></el-option>
            </el-select>
          </div>
          <div class="form-item">
            <label class="form-label">事件紧急程度</label>
            <el-select v-model="formData.eventLevel" placeholder="事件紧急程度" class="form-select">
              <el-option label="紧急" value="紧急"></el-option>
              <el-option label="重要" value="重要"></el-option>
              <el-option label="一般" value="一般"></el-option>
            </el-select>
          </div>
        </div>
      </div>

      <!-- 处置信息 -->
      <div class="form-section">
        <div class="section-header">流转信息</div>
        <div class="form-row">
          <div class="form-item">
            <label class="form-label">流转系统</label>
            <el-select v-model="formData.lzxt" placeholder="请选择流转系统" class="form-select">
              <el-option label="一网统管" value="一网统管"></el-option>
            </el-select>
          </div>
        </div>
        <div class="form-row">
          <div class="form-item full-width">
            <label class="form-label">处置人员</label>
            <el-input v-model="formData.handlePerson" placeholder="请输入处置人员" class="form-input"></el-input>
          </div>
        </div>
      </div>

      <div style="text-align: center">
        <el-button type="primary" style="width: 140px;font-size:32px;" @click="close">提交</el-button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'WarningDetailDialog',
  components: {},
  props: {
    msgObj: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      formData: {
        mc: '', // 事件名称
        ms: '', // 事件描述
        sjdd: '', // 事件地点
        sjly: '', // 事件来源
        eventLevel: '', // 事件紧急程度
        lzxt: '', // 流转系统
        handlePerson: '', // 处置人员
      },
    }
  },
  mounted() {},
  watch: {
    msgObj: {
      handler(newVal) {
        if (!newVal) return
        Object.assign(this.formData, {
          mc: newVal.mc || '',
          ms: newVal.ms || '',
          sjdd: newVal.sjdd || '',
          sjly: '人口活力',
          eventLevel: '', // 事件紧急程度
          lzxt: '一网统管', // 流转系统
          handlePerson: '', // 处置人员
        })
      },
      deep: true,
      immediate: true,
    },
  },
  methods: {
    close() {
      this.$emit('close')
    },
  },

  beforeDestroy() {},
}
</script>

<style lang="less">
.change-content {
  height: 100%;
  background: rgba(0, 61, 61, 0.8);
  border-radius: 4px;
  padding: 40px !important;
  overflow-y: auto;

  .form-container {
    .form-section {
      margin-bottom: 50px;

      .section-header {
        color: #00CCE3;
        font-size: 32px;
        font-weight: bold;
        margin-bottom: 30px;
        padding-bottom: 16px;
      }

      .form-row {
        display: flex;
        gap: 40px;
        margin-bottom: 30px;

        .form-item {
          flex: 1;

          &.full-width {
            flex: 1 1 100%;
          }

          .form-label {
            display: block;
            color: #fff;
            font-size: 28px;
            margin-bottom: 16px;
          }

          .form-select {
            width: 100%;

            .el-input__inner {
              background: rgba(0, 61, 61, 0.8);
              color: #fff;
              height: 72px !important;
              line-height: 72px !important;
              font-size: 28px;

              &:focus {
                border-color: #00CCE3;
              }

              &::placeholder {
                color: rgba(255, 255, 255, 0.5);
              }
            }

            .el-input__suffix {
              .el-input__suffix-inner {
                .el-select__caret {
                  color: #00CCE3;
                }
              }
            }
          }

          .form-input {
            width: 100%;

            .el-input__inner {
              background: rgba(0, 61, 61, 0.8);
              color: #fff;
              height: 72px !important;
              line-height: 72px !important;
              font-size: 28px;

              &:focus {
                border-color: #00CCE3;
              }

              &::placeholder {
                color: rgba(255, 255, 255, 0.5);
              }
            }
          }
        }
      }
    }
  }
}
</style>
