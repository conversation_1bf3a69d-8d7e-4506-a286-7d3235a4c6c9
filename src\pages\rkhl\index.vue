<!--
 * @Description: 
 * @Version: 1.0
 * @Autor: wjb
 * @Date: 2025-04-14 11:38:22
 * @LastEditors: wjb
 * @LastEditTime: 2025-10-24 09:27:31
-->
<template>
  <div class="qyzf-container">
    <div class="inneLeft">
      <rkhlLeft class="animate__animated animate__fadeInLeft"/>
    </div>
    <div class="midBottom animate__animated animate__fadeIn">
      <rkhlCenter />
    </div>
    <div class="innerRight">
      <rkhlRight class="animate__animated animate__fadeInRight" @open-slsw="onOpenSlsw"/>
    </div>

    <!-- 弹窗：slsw -->
    <slsw v-if="visible" :initialTab="(dialogPayload && dialogPayload.tabIndex) || 0" :visible="visible" @close="onCloseSlsw"/>
  </div>
</template>

<script>
import wrapbox from '@/components/wrapbox'
import rkhlLeft from './components/rkhlLeft.vue'
import rkhlRight from './components/rkhlRight.vue'
import rkhlCenter from './components/rkhlCenter.vue'
import slsw from './components/slsw.vue'
export default {
  name: 'RkhlIndex',
  data() {
    return {
      visible: false,
      dialogPayload: null,
    }
  },
  components: {
    wrapbox,
    rkhlLeft,
    rkhlRight,
    rkhlCenter,
    slsw
  },
  mounted() {},
  methods: {
    onOpenSlsw(payload) {
      this.visible = true
      this.dialogPayload = payload
    },
    onCloseSlsw() {
      this.visible = false
      this.dialogPayload = null
    },
  }
}
</script>

<style scoped lang="less">
.qyzf-container {
  position: relative;
  width: 100%;
  height: 100%;

  .inneLeft {
    position: absolute;
    top: 229px;
    z-index: 2;
    display: flex;
    justify-content: space-between;
  }

  .innerRight {
    position: absolute;
    top: 229px;
    right: 0;
    z-index: 2;
    display: flex;
    justify-content: space-between;
  }
  .midBottom {
    position: absolute;
    left: 1660px;
    top: 229px;
    z-index: 2;
  }
}
</style>
