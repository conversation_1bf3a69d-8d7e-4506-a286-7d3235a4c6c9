import Vue from 'vue'
import ElementUI from 'element-ui'
import App from './App.vue'
import { initRouter } from './router'
import store from './store'
import 'element-ui/lib/theme-chalk/index.css'
import './index.less'
import vueSeamlessScroll from 'vue-seamless-scroll' // 循环滚动
import * as echarts from 'echarts'
import mixin from '@/minxins/setTimerMinxin'
import { numberFilter } from '@/utils/index'
import moment from 'moment'
import 'animate.css'
import '@/assets/font/font.css'
import '@/utils/echarts-wordCloud.js'
import jquery from 'jquery'
import VueCountTo from 'vue-count-to'
import OptionsSwitcher from '@/components/OptionsSwitcher.vue'

Vue.component('count-to', VueCountTo)
Vue.component('OptionsSwitcher', OptionsSwitcher)
Vue.prototype.$moment = moment
Vue.use(vueSeamlessScroll)
const router = initRouter()
Vue.use(ElementUI)
// Vue.use(Image)
Vue.mixin(mixin)
Vue.filter('numberFilter', numberFilter)

Vue.config.productionTip = false
Vue.prototype.$bus = new Vue()
Vue.prototype.$pageWidth = 7680
Vue.prototype.$pageHeight = 2160
Vue.prototype.$jquery = jquery
Vue.prototype.$echarts = echarts
Vue.prototype.$EventBus = new Vue()

// Vue.prototype.timeMixin = mixin;

router.beforeEach((to, from, next) => {
  /* 路由发生变化修改页面title */
  if (to.meta.title) {
    document.title = to.meta.title
  }
  next()
})

new Vue({
  router,
  store,
  render: (h) => h(App),
}).$mount('#app')
