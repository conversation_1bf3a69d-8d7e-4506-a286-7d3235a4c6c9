<template>
  <el-dialog
    title=""
    :top="winTop"
    ref="requestDialog"
    custom-class="custom-class-dialog"
    :show-close="false"
    :modal="true"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :visible.sync="dialogVisible"
    width="2209px"
    :modal-append-to-body="true"
    :append-to-body="true"
    @open="dialogOpen"
  >
    <div class="dialog-wrapper">
      <!-- 标题栏 -->
      <div class="dialog-header">
        <div class="header-title">企业诉求详情</div>
        <i class="el-icon-close close-btn" @click="closeDialog"></i>
      </div>

      <!-- 内容区域 -->
      <div class="dialog-content">
        <!-- 基本信息 -->
        <div class="section">
          <div v-if="detailData.status == '超期处置'">
            <el-button type="primary" style="font-size: 32px; margin-bottom: 24px" @click="doBan">督办</el-button>
          </div>
          <div class="section-title">
            <div class="title-icon"></div>
            <span>基本信息</span>
          </div>
          <div class="info-table">
            <div class="info-row">
              <div class="info-cell label">诉求企业</div>
              <div class="info-cell value">{{ detailData.enterprise_name }}</div>
              <div class="info-cell label">诉求类型</div>
              <div class="info-cell value">{{ detailData.claim_type }}</div>
            </div>
            <div class="info-row">
              <div class="info-cell label">联系人</div>
              <div class="info-cell value">{{ detailData.enterprise_person }}</div>
              <div class="info-cell label">联系方式</div>
              <div class="info-cell value">{{ detailData.enterprise_mobile }}</div>
            </div>
            <div class="info-row">
              <div class="info-cell label">诉求信息</div>
              <div class="info-cell value full-width">{{ detailData.claim_title }}</div>
            </div>
          </div>
        </div>

        <!-- 处置信息 -->
        <div class="section">
          <div class="section-title">
            <div class="title-icon"></div>
            <span>处置信息</span>
          </div>
          <div class="process-flow">
            <!-- 接收 -->
            <div class="process-card">
              <div class="card-title">申请</div>
              <div class="card-content">
                <div class="card-item">
                  <span class="card-label">申请人:</span>
                  <span class="card-value">{{ detailData.enterprise_person }}</span>
                </div>
                <div class="card-item">
                  <span class="card-label">部门:</span>
                  <span class="card-value">{{ detailData.enterprise_name }}</span>
                </div>
                <div class="card-item">
                  <span class="card-label">接收时间:</span>
                  <span class="card-value">{{ detailData.create_time }}</span>
                </div>
              </div>
              <div class="arrow-icon">»</div>
            </div>
            <!-- 处置 -->
            <div class="process-card" :class="detailData.status == '超期处置' ? 'overdue' : ''">
              <div class="card-title">处置</div>
              <div class="card-content">
                <div class="card-item">
                  <span class="card-label">处置员:</span>
                  <span class="card-value">{{ detailData.transfer_person }}</span>
                </div>
                <div class="card-item">
                  <span class="card-label">部门:</span>
                  <span class="card-value">{{ detailData.transfer_person }}</span>
                </div>
                <div class="card-item">
                  <span class="card-label">受理时间:</span>
                  <span class="card-value">{{ detailData.create_time }}</span>
                </div>
                <div class="card-item full-width">
                  <span class="card-label">处置结果:</span>
                  <span class="card-value">{{ detailData.handle_status }}</span>
                </div>
              </div>
              <div class="arrow-icon">»</div>
            </div>

            <!-- 归档 -->
            <div class="process-card">
              <div class="card-title">归档</div>
              <div class="card-content">
                <div class="card-item">
                  <span class="card-label">审核员:</span>
                  <span class="card-value">{{ detailData.transfer_person }}</span>
                </div>
                <div class="card-item">
                  <span class="card-label">部门:</span>
                  <span class="card-value">{{ detailData.transfer_person }}</span>
                </div>
                <div class="card-item">
                  <span class="card-label">受理时间:</span>
                  <span class="card-value">{{ detailData.create_time }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script>
import $ from 'jquery'

export default {
  name: 'EnterpriseRequestDialog',
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    detailData: {
      type: Object,
      default: () => ({
        status: '',
        company_name: '',
        request_type: '',
        contact_person: '',
        contact_phone: '',
        request_info: '',
        accept_person: '',
        accept_dept: '',
        accept_time: '',
        receive_person: '',
        receive_dept: '',
        receive_time: '',
        handle_person: '',
        handle_dept: '',
        handle_time: '',
        handle_result: '',
        archive_person: '',
        archive_dept: '',
        archive_time: '',
      }),
    },
  },
  data() {
    return {
      dialogVisible: false,
      winTop: '40px',
    }
  },
  watch: {
    visible(val) {
      this.dialogVisible = val
      if (val) {
        this.$nextTick(() => {
          setTimeout(() => {
            this.winTop =
              (document.documentElement.clientHeight - $(this.$refs.requestDialog.$el).find('.el-dialog').height()) /
                2 +
              'px'
          }, 100)
        })
      }
    },
  },
  methods: {
    closeDialog() {
      this.$emit('update:visible', false)
      this.$emit('close')
    },
    dialogOpen() {
      this.$emit('open')
    },
    doBan() {
      this.$message({
        message: '已督办',
        type: 'success',
      })
    },
  },
}
</script>

<style lang="less" scoped>
/deep/.el-dialog {
  background: #002b37;
  background-size: 100% 100%;
  border: 2px solid #23daff;
  border-radius: 0px;
  box-shadow: inset 0px 0px 55px 0px #21f0e2;

  .el-dialog__header {
    display: none;
  }

  .el-dialog__body {
    padding: 0;
  }
}

.dialog-wrapper {
  width: 100%;
  height: 1389px;
}

.dialog-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 120px;
  padding: 0 40px;
  background: linear-gradient(90deg, rgba(35, 218, 255, 0.4) 0%, rgba(35, 218, 255, 0.1) 100%);

  .header-title {
    font-size: 56px;
    font-weight: bold;
    color: #ffffff;
  }

  .close-btn {
    font-size: 64px;
    color: #ffffff;
    cursor: pointer;
    transition: all 0.3s;

    &:hover {
      color: #23daff;
    }
  }
}

.dialog-content {
  padding: 40px;
  height: calc(1389px - 200px);
  display: flex;
  flex-direction: column;
}

.section {
  margin-bottom: 40px;

  &:last-child {
    flex: 1;
    display: flex;
    flex-direction: column;
    margin-bottom: 0;
  }

  .section-title {
    display: flex;
    align-items: center;
    margin-bottom: 30px;

    .title-icon {
      width: 54px;
      height: 54px;
      margin-right: 16px;
      background: url('~@/assets/qyzf/subtitle-icon.png') no-repeat center;
      background-size: contain;
    }

    span {
      font-size: 42px;
      font-weight: bold;
      color: #ffc460;
    }
  }
}

.info-table {
  background: rgba(13, 61, 77, 0.5);
  border: 1px solid rgba(35, 218, 255, 0.3);

  .info-row {
    display: flex;
    border-bottom: 1px solid rgba(35, 218, 255, 0.3);

    &:last-child {
      border-bottom: none;
    }

    .info-cell {
      padding: 32px 40px;
      font-size: 32px;
      border-right: 1px solid rgba(35, 218, 255, 0.3);

      &:last-child {
        border-right: none;
      }

      &.label {
        background: rgba(13, 61, 77, 0.3);
        color: #b8d8e8;
        min-width: 200px;
        flex-shrink: 0;
      }

      &.value {
        color: #ffffff;
        flex: 1;

        &.full-width {
          flex: 3;
        }
      }
    }
  }
}

.process-flow {
  display: flex;
  justify-content: space-between;
  gap: 120px;
  flex: 1;
}

.process-card {
  flex: 1;
  background: rgba(13, 61, 77, 0.6);
  border: 1px solid rgba(35, 218, 255, 0.3);
  border-radius: 4px;
  padding: 30px 24px;
  position: relative;

  &.overdue {
    border-color: red !important;
  }

  .card-title {
    font-size: 38px;
    font-weight: bold;
    color: #23daff;
    text-align: center;
    margin-bottom: 24px;
    padding-bottom: 16px;
    border-bottom: 1px solid rgba(35, 218, 255, 0.3);
  }

  .card-content {
    .card-item {
      margin-bottom: 16px;
      font-size: 32px;

      &.full-width {
        margin-top: 20px;
      }

      .card-label {
        color: #b8d8e8;
        display: inline-block;
        min-width: 140px;
      }

      .card-value {
        color: #ffffff;
      }
    }
  }

  .arrow-icon {
    position: absolute;
    right: -75px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 60px;
    color: #23daff;
    z-index: 1;
  }

  &:last-child .arrow-icon {
    display: none;
  }
}
</style>

