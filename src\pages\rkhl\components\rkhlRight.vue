<template>
  <div class="rkhl-right">
    <div class="section population-monitor">
      <MainTitle title="详细人口情况" size="large" />
      <div class="section-content">
        <!-- 第一行 -->
        <div class="row">
          <!-- 户籍人口趋势 -->
          <HouseholdPopulationStructure />
          <!-- 新增人口情况 -->
          <FertilityStatus />
          <!-- 人口城乡结构 -->
          <ResidentPopulationTrend />
          <!-- 结婚登记情况 -->
          <!-- <MarriageRegistration /> -->
        </div>
        <!-- 第二行 -->
        <div class="row">
          <!-- 人口流动趋势 -->
          <MobilityTrend />
          <!-- 人员流入流出情况 -->
          <PopulationFlow @open-slsw="handleOpenSlsw" />
          <!-- 外来人口来源地 -->
          <MigrantPopulationFeatures />
        </div>
      </div>
    </div>

    <div class="section area-monitor">
      <MainTitle title="人口保障与发展情况" size="large"></MainTitle>
      <div class="section-content">
        <div class="row">
          <!-- 新增就业情况 -->
          <NewEmploymentStatus />
          <!-- 养老保险情况 -->
          <PensionInsuranceStatus />
          <!-- 医疗保险情况 -->
          <MedicalInsuranceStatus />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import MainTitle from '@/components/MainTitle.vue'
import ResidentPopulationTrend from '../right/ResidentPopulationTrend.vue'
import FertilityStatus from '../right/FertilityStatus.vue'
import MarriageRegistration from '../right/MarriageRegistration.vue'
import NewEmploymentStatus from '../right/NewEmploymentStatus.vue'
import PensionInsuranceStatus from '../right/PensionInsuranceStatus.vue'
import MedicalInsuranceStatus from '../right/MedicalInsuranceStatus.vue'
import HouseholdPopulationStructure from '../right/HouseholdPopulationStructure.vue'
import MobilityTrend from '../right/MobilityTrend.vue'
import MigrantPopulationFeatures from '../right/MigrantPopulationFeatures.vue'
import PopulationFlow from './PopulationFlow.vue'

export default {
  name: 'RkhlRight',
  components: {
    MainTitle,
    ResidentPopulationTrend,
    FertilityStatus,
    MarriageRegistration,
    NewEmploymentStatus,
    PensionInsuranceStatus,
    MedicalInsuranceStatus,
    HouseholdPopulationStructure,
    MobilityTrend,
    MigrantPopulationFeatures,
    PopulationFlow,
  },
  methods: {
    handleOpenSlsw(payload) {
      this.$emit('open-slsw', payload)
    },
  },
}
</script>

<style lang="less" scoped>
* {
  margin: 0;
  padding: 0;
}

.rkhl-right {
  width: 1550px;
  height: 1862px;
  padding: 10px 0 0px;
  margin-right: 50px;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;

  .section {
    width: 100%;
    margin-bottom: 40px;

    &.population-monitor {
      height: 66.67%; /* 2/3 的高度 */
    }

    &.area-monitor {
      height: 33.33%; /* 1/3 的高度 */
    }

    .section-content {
      width: 100%;
      height: calc(100% - 110px);
      position: relative;

      .row {
        display: flex;
        gap: 12px;
        margin-bottom: 20px;
        height: calc(50% - 10px);

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
}
</style>
