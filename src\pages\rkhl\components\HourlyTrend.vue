<template>
  <div class="chart-item">
    <SubTitle title="24小时实时人口变化趋势" />
    <div class="chart-container">
      <div class="chart-controls"></div>
      <div id="hourlyTrend" class="chart"></div>
    </div>
  </div>
</template>

<script>
import OptionsSwitcher from '@/components/OptionsSwitcher.vue'
import SubTitle from '@/components/SubTitle.vue'
import { getCsdnInterface1 } from '@/api/csdnIndexApi'

export default {
  name: 'HourlyTrend',
  components: {
    SubTitle,
    OptionsSwitcher,
  },
  data() {
    return {
      chartData: [],
      loading: false,
    }
  },
  mounted() {
    this.fetchData()
  },
  methods: {
    // 获取24小时趋势数据
    async fetchData() {
      try {
        this.loading = true
        // 获取 SubTitle 的 title 属性作为 type 参数
        const titleType = '24小时实时人口变化趋势'
        const response = await getCsdnInterface1('qyhx_pop_ssrkbh', { type: titleType })

        console.log('24小时实时人口变化趋势接口返回数据:', response.data)

        // 处理接口返回的数据
        if (response.data && response.data.successful && response.data.data) {
          this.chartData = this.processChartData(response.data.data)
        } else {
          // 如果接口返回空数据或失败，使用默认数据
          this.chartData = {
            xData: ['06:18', '09:18', '12:18', '15:18', '18:18', '21:18', '24:18'],
            yData: [15000, 12000, 8000, 5000, 11000, 16000, 14000],
          }
        }
      } catch (error) {
        console.error('获取24小时实时人口变化趋势数据失败:', error)
        // 请求失败时使用默认数据
        this.chartData = {
          xData: ['06:18', '09:18', '12:18', '15:18', '18:18', '21:18', '24:18'],
          yData: [15000, 12000, 8000, 5000, 11000, 16000, 14000],
        }
      } finally {
        this.loading = false
        this.initHourlyTrend()
      }
    },

    // 处理图表数据
    processChartData(data) {
      // 根据实际接口返回的数据结构进行处理
      if (Array.isArray(data) && data.length > 0) {
        return {
          xData: data.map((item, index) => item.hour || `${6 + index * 3}:00`),
          yData: data.map((item) => item.total_count || 0),
        }
      }

      // 如果数据格式不符合预期，返回默认数据
      return {
        xData: ['06:18', '09:18', '12:18', '15:18', '18:18', '21:18', '24:18'],
        yData: [15000, 12000, 8000, 5000, 11000, 16000, 14000],
      }
    },

    // 24小时趋势折线图
    initHourlyTrend() {
      const chart = this.$echarts.init(document.getElementById('hourlyTrend'))
      const option = {
        tooltip: {
          trigger: 'axis',
          backgroundColor: 'rgba(0, 0, 0, 0.8)',
          borderColor: '#00c0ff',
          textStyle: { color: '#fff', fontSize: 24 },
          formatter: function (params) {
            const data = params[0]
            const value = data.value >= 10000 ? (data.value / 10000).toFixed(1) + '万人' : data.value + '人'
            return `${data.name}<br/>人口数量: ${value}`
          },
        },
        grid: {
          left: '14%',
          right: '5%',
          top: '20%',
          bottom: '20%',
        },
        xAxis: {
          type: 'category',
          data: this.chartData.xData,
          axisLabel: { color: '#fff', fontSize: 24 },
          axisLine: { lineStyle: { color: '#333' } },
        },
        yAxis: {
          type: 'value',
          show: true,
          name: '单位:(万人)',
          nameLocation: 'end',
          nameTextStyle: {
            color: '#fff',
            fontSize: 20,
            padding: [0, 0, 10, 0],
          },
          axisLabel: {
            show: true,
            color: '#fff',
            fontSize: 24,
            formatter: function (value) {
              return value.toFixed(0)
            },
          },
          axisLine: {
            show: false,
            lineStyle: { color: '#fff' },
          },
          axisTick: {
            show: false,
            lineStyle: { color: '#fff' },
          },
          splitLine: {
            show: false,
            lineStyle: { color: '#333' },
          },
        },
        series: [
          {
            type: 'line',
            data: this.chartData.yData,
            smooth: true,
            lineStyle: { color: '#00c0ff', width: 2 },
            itemStyle: { color: '#00c0ff' },
            areaStyle: {
              color: new this.$echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: 'rgba(0, 192, 255, 0.3)' },
                { offset: 1, color: 'rgba(0, 192, 255, 0)' },
              ]),
            },
          },
        ],
      }
      chart.setOption(option)
    },
  },
}
</script>

<style lang="less" scoped>
.chart-item {
  flex: 1;
  background: transparent;
  border-radius: 8px;
  padding: 10px;
  border: none;

  .chart-container {
    position: relative;

    .chart-controls {
      position: absolute;
      top: 10px;
      right: 10px;
      display: flex;
      gap: 8px;
      z-index: 10;

      .control-btn {
        width: 120px;
        height: 40px;
        font-size: 24px;
        font-weight: 400;
        color: #bfbcbc;
        background: rgba(0, 74, 166, 0.3);
        border: 2px solid #215293;
        text-align: center;
        line-height: 40px;
        cursor: pointer;
        transition: all 0.3s ease;

        &.active {
          color: #ffffff;
          font-weight: 700;
          background: rgba(0, 74, 166, 0.8);
        }

        &:hover {
          background: rgba(0, 74, 166, 0.6);
          color: #ffffff;
        }
      }
    }

    .chart {
      width: 100%;
      height: 470px;
    }
  }
}
</style>
