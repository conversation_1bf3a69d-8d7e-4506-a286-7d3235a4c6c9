<template>
  <div class="chart-item">
    <div class="clickable-title" @click="handleTitleClick">
      <SubTitle title="人员流入流出情况" />
    </div>
    <div class="chart-container">
      <div id="populationFlow" class="chart"></div>
    </div>
  </div>
</template>

<script>
import { getCsdnInterface1 } from '@/api/csdnIndexApi'
import SubTitle from '@/components/SubTitle.vue'

export default {
  name: 'PopulationFlow',
  components: {
    SubTitle,
  },
  mounted() {
    this.fetchFlowData()
  },
  methods: {
    // 处理标题点击事件
    handleTitleClick() {
      this.$emit('open-slsw', { tabIndex: 0 })
    },

    // 获取人员流入流出数据
    async fetchFlowData() {
      try {
        // 使用与FertilityStatus相同的接口调用方式
        const titleType = '人员流入流出情况'
        const response = await getCsdnInterface1('rkhl_test', { type: titleType })

        console.log('人员流入流出情况接口返回数据:', response.data)

        // 处理接口返回的数据
        if (response.data && response.data.successful && response.data.data) {
          const processedData = this.processFlowData(response.data.data)
          this.getLineCharts(
            'populationFlow',
            processedData.xdata,
            processedData.data1,
            processedData.data2,
            processedData.data3,
            processedData.data4
          )
        }
      } catch (error) {
        console.error('获取人员流入流出情况数据失败:', error)
      }
    },

    // 处理流入流出数据格式
    processFlowData(data) {
      if (!Array.isArray(data) || data.length === 0) {
        // 返回默认数据
        return {
          xdata: ['2021', '2022', '2023', '2024'],
          data1: [1574, 2123, 1295, 5098],
          data2: [6521, 3931, 5921, 5745],
          data3: [1574, 2123, 1295, 5098],
          data4: [6521, 3931, 5921, 5745],
        }
      }

      const xdata = []
      const data1 = []
      const data2 = []
      const data3 = []
      const data4 = []

      data.forEach((item) => {
        xdata.push(item.name) // 年份
        // 直接使用原始数值，不转换单位
        data1.push(parseInt(item.value))
        data2.push(parseInt(item.value2))
        data3.push(parseInt(item.value1))
        data4.push(parseInt(item.value3))
      })

      return {
        xdata,
        data1,
        data2,
        data3,
        data4,
      }
    },

    // 人员流入流出对比图表绘制
    getLineCharts(dom, xdata, data1, data2, data3, data4) {
      let myEc = this.$echarts.init(document.getElementById(dom))
      const option = {
        grid: {
          left: '5%',
          right: '6%',
          top: '20%',
          bottom: '1%',
          containLabel: true,
        },
        legend: {
          data: ['省内流入', '省内流出', '省外流入', '省外流出'],
          top: '5%',
          icon: 'circle',
          itemWidth: 12,
          itemHeight: 12,
          itemGap: 10,
          textStyle: {
            color: '#fff',
            fontSize: 20,
            fontFamily: 'SourceHanSansCN-Medium',
          },
        },
        tooltip: {
          show: true,
          trigger: 'axis',
          textStyle: {
            fontSize: 24,
            color: '#fff',
          },
          backgroundColor: 'rgba(51, 51, 51, 0.7)',
          borderWidth: 0,
        },
        xAxis: [
          {
            type: 'category',
            axisLine: {
              lineStyle: {
                color: 'rgb(119,179,241,.4)', // 颜色
                width: 1, // 粗细
              },
            },
            axisTick: {
              show: false,
            },
            axisLabel: {
              interval: 0, // 显示所有年份标签
              textStyle: {
                color: '#fff',
                fontSize: 24,
              },
            },
            data: xdata,
          },
        ],
        yAxis: [
          {
            type: 'value',
            name: '单位:(人)',
            nameLocation: 'end',
            nameTextStyle: {
              color: '#fff',
              fontSize: 20,
              padding: [0, 0, 10, 0],
            },
            min: function (value) {
              return Math.max(0, value.min - 500)
            },
            splitLine: {
              lineStyle: {
                color: 'rgb(119,179,241,.4)',
              },
            },
            axisLabel: {
              textStyle: {
                color: '#fff',
                fontSize: 24,
                fontFamily: 'SourceHanSansCN-Medium',
              },
            },
          },
        ],
        series: [
          {
            name: '省内流入',
            type: 'line',
            symbolSize: 10,
            itemStyle: {
              color: '#00C0FF',
              borderColor: '#00C0FF',
              borderWidth: 1,
            },
            data: data1,
          },
          {
            name: '省内流出',
            type: 'line',
            symbolSize: 10,
            itemStyle: {
              color: '#FFC460',
              borderColor: '#FFC460',
              borderWidth: 1,
            },
            data: data2,
          },
          {
            name: '省外流入',
            type: 'line',
            symbolSize: 10,
            itemStyle: {
              color: '#00C0FF',
              borderColor: '#00C0FF',
              borderWidth: 1,
            },
            data: data3,
          },
          {
            name: '省外流出',
            type: 'line',
            symbolSize: 10,
            itemStyle: {
              color: '#FFC460',
              borderColor: '#FFC460',
              borderWidth: 1,
            },
            data: data4,
          },
        ],
      }

      myEc.setOption(option)
      myEc.getZr().on('mousemove', (param) => {
        myEc.getZr().setCursorStyle('default')
      })
    },
  },
}
</script>

<style lang="less" scoped>
.chart-item {
  flex: 1;
  background: transparent;
  border-radius: 8px;
  padding: 10px;
  border: none;

  .clickable-title {
    cursor: pointer;
    transition: opacity 0.3s ease;

    &:hover {
      opacity: 0.8;
    }
  }

  .chart-container {
    position: relative;

    .chart-controls {
      position: absolute;
      top: 10px;
      right: 10px;
      display: flex;
      gap: 8px;
      z-index: 10;

      .control-btn {
        width: 120px;
        height: 40px;
        font-size: 24px;
        font-weight: 400;
        color: #bfbcbc;
        background: rgba(0, 74, 166, 0.3);
        border: 2px solid #215293;
        text-align: center;
        line-height: 40px;
        cursor: pointer;
        transition: all 0.3s ease;

        &.active {
          color: #ffffff;
          font-weight: 700;
          background: rgba(0, 74, 166, 0.8);
        }

        &:hover {
          background: rgba(0, 74, 166, 0.6);
          color: #ffffff;
        }
      }
    }

    .chart {
      width: 100%;
      height: 470px;
    }
  }
}
</style>
