<template>
  <div class="chart-item large">
    <SubTitle title="连续多日总人数变化情况" />
    <div class="chart-container">
      <!-- 人流量选择器 -->
      <!-- <div class="flow-selector">
        <el-select v-model="startName" @change="queryChart01Data" filterable placeholder="请选择">
          <el-option
            v-for="item in flowList"
            :title="item.point_name"
            :key="item.point_name"
            :label="item.point_name"
            :value="item.point_name"
          ></el-option>
        </el-select>
      </div> -->

      <div id="governmentFlow" class="chart"></div>
    </div>
  </div>
</template>

<script>
import SubTitle from '@/components/SubTitle.vue'
import { getCsdnInterface1 } from '@/api/csdnIndexApi'

export default {
  name: 'GovernmentFlow',
  components: {
    SubTitle,
  },
  props: {
    currentLevel: {
      type: String,
      default: '重点场所',
    },
    selectedArea: {
      type: String,
      default: '安陆市东大时代广场',
    },
  },
  data() {
    return {
      sqActiveIndex: 0,
      startName: null,
      code: null,
      flowList: [],
    }
  },
  mounted() {
    this.initData()
    // 监听级别变化事件
    // this.$EventBus.$on('levelChanged', this.handleLevelChanged)
  },
  beforeDestroy() {
    // 移除事件监听
    // this.$EventBus.$off('levelChanged', this.handleLevelChanged)
  },
  watch: {
    selectedArea: {
      handler(newArea) {
        console.log('GovernmentFlow 接收到区域变化:', newArea)
        // 当区域变化时重新请求数据
        this.queryChart01Data()
      },
      immediate: false, // 不立即执行,避免重复请求
    },
  },
  methods: {
    // 处理级别变化
    handleLevelChange(level) {
      this.initData()
    },

    // 处理事件总线的级别变化
    handleLevelChanged(value) {
      console.log('接收到级别变化事件:', value)
      // 根据级别值找到对应的商圈分类项

      console.log('找到对应的商圈分类:')
      this.sqClassChange(0, value.classification_code)
    },

    // 初始化数据
    initData() {
      this.queryChart01Data()
    },

    // 商圈分类切换
    async sqClassChange(_, item) {
      this.sqActiveIndex = item.classification_code
      console.log('商圈分类切换', item)
      try {
        const res = await getCsdnInterface1('csrk_class_xq', {
          classificationCode: item.classification_code,
        })
        this.flowList = res.data || []
        if (this.flowList.length > 0) {
          this.queryChart01Data(this.flowList[0].point_name)
        }
      } catch (error) {
        console.error('获取商圈详情失败:', error)
      }
    },

    // 查询图表数据
    async queryChart01Data() {
      try {
        const res = await getCsdnInterface1('qyhx_pop_sqrll', {
          type: '商圈人流量',
          name: this.selectedArea, // 传递选中的区域名称
        })

        const data = []
        const value = []
        const value7 = []

        res.data.data.forEach((ele) => {
          data.push(ele.stat_date)
          value.push(ele.max_peak_count)
          value7.push(ele.avg_peak_count)
        })

        this.renderChart(data, value, value7)
      } catch (error) {
        console.error('获取图表数据失败:', error)
      }
    },

    // 渲染图表
    renderChart(data, value, value7) {
      const chartDom = document.getElementById('governmentFlow')
      if (!chartDom) return

      // 销毁已存在的实例
      this.$echarts.dispose(chartDom)
      const chart = this.$echarts.init(chartDom)

      const option = {
        textStyle: {
          color: '#fff',
        },
        legend: {
          icon: 'circle',
          top: '5%',
          itemWidth: 12,
          itemHeight: 12,
          itemGap: 30,
          textStyle: {
            color: '#fff',
            fontSize: 22,
            fontFamily: 'SourceHanSansCN-Medium',
          },
        },
        grid: {
          left: '5%',
          right: '5%',
          top: '20%',
          bottom: '1%',
          containLabel: true,
        },
        tooltip: {
          show: true,
          trigger: 'axis',
          textStyle: {
            fontSize: 22,
            color: '#fff',
          },
          backgroundColor: 'rgba(51, 51, 51, 0.7)',
          borderWidth: 0,
        },
        xAxis: {
          containLabel: false,
          boundaryGap: true,
          data: data,
          axisLabel: {
            showMinLabel: true,
            showMaxLabel: true,
            margin: 15,
            textStyle: {
              color: '#fff',
            },
            fontSize: 24,
          },
          interval: 2,
        },
        yAxis: {
          type: 'value',
          name: '单位:人',
          nameTextStyle: {
            color: '#fff',
            fontSize: 20,
            padding: 5,
          },
          axisLabel: {
            textStyle: {
              color: '#fff',
            },
            fontSize: 22,
          },
          splitLine: {
            show: true,
            lineStyle: {
              type: 'solid',
              color: 'rgb(66,106,149,0.4)',
            },
          },
          axisLine: {
            lineStyle: {
              type: 'solid',
              color: 'rgba(88, 138, 139,.6)',
              width: '0',
            },
          },
        },
        series: [
          {
            name: '近7日均值',
            type: 'line',
            data: value7,
            smooth: true,
            symbol: 'circle',
            showSymbol: false,
            color: '#d4dd48',
          },
          {
            name: '当日峰值',
            type: 'line',
            data: value,
            smooth: true,
            symbol: 'circle',
            showSymbol: false,
            color: 'rgba(15,139,198,0.7)',
            itemStyle: {
              color: 'rgba(0,194,255,0.8)',
              borderColor: '#fff',
            },
            areaStyle: {
              normal: {
                color: new this.$echarts.graphic.LinearGradient(
                  0,
                  0,
                  0,
                  1,
                  [
                    { offset: 0, color: 'rgba(0,194,255,0.8)' },
                    { offset: 1, color: 'rgba(0,194,255,0.2)' },
                  ],
                  true
                ),
                shadowColor: 'rgba(0,194,255,1)',
              },
            },
          },
        ],
      }

      chart.setOption(option)

      // 设置鼠标样式
      chart.getZr().on('mousemove', () => {
        chart.getZr().setCursorStyle('default')
      })
    },
  },
}
</script>

<style lang="less" scoped>
.chart-item {
  flex: 1;
  background: transparent;
  border-radius: 8px;
  padding: 10px;
  border: none;

  &.large {
    flex: 1.5;
  }

  .chart-container {
    position: relative;

    .flow-selector {
      position: absolute;
      top: 10px;
      right: 10px;
      z-index: 10;

      /deep/ .el-select {
        width: 200px;

        .el-input__inner {
          cursor: pointer;
          width: 200px;
          height: 35px;
          border-radius: 20px;
          color: #fff;
          background-color: #132c4ed0;
          border: 1px solid #359cf8;
          font-size: 16px;
        }
      }

      /deep/ .el-select-dropdown {
        background-color: rgba(19, 44, 78, 0.816);
        border: 1px solid #1b5ad7;

        .el-select-dropdown__item {
          color: #d2d3d4;
          font-size: 16px;

          &.selected {
            color: #0167ff;
          }

          &.hover,
          &:hover {
            background-color: #3f6db3;
          }
        }
      }
    }

    .chart {
      width: 100%;
      height: 400px;
    }
  }
}
</style>
