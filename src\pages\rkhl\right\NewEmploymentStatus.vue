<template>
  <div class="chart-item">
    <SubTitle title="新增就业情况" />
    <div class="chart-container">
      <div id="newEmploymentStatus" class="chart"></div>
    </div>
  </div>
</template>

<script>
import SubTitle from '@/components/SubTitle.vue'
import { getCsdnInterface1 } from '@/api/csdnIndexApi'

export default {
  name: 'NewEmploymentStatus',
  components: { SubTitle },
  data() {
    return {
      chartData: [],
      loading: false,
    }
  },
  mounted() {
    this.fetchData()
  },
  methods: {
    async fetchData() {
      try {
        this.loading = true
        // 获取 SubTitle 的 title 属性作为 type 参数
        const titleType = '新增就业情况'
        const response = await getCsdnInterface1('rkhl_test', { type: titleType })

        console.log('接口返回数据:', response.data)

        // 处理接口返回的数据
        if (response.data && response.data.successful && response.data.data) {
          this.chartData = this.processChartData(response.data.data)
        } else {
          // 如果接口返回空数据或失败，使用空数组
          this.chartData = []
        }
      } catch (error) {
        console.error('获取数据失败:', error)
        // 请求失败时使用空数组
        this.chartData = []
      } finally {
        this.loading = false
        this.initChart()
      }
    },

    processChartData(data) {
      // 处理新增就业情况数据
      if (Array.isArray(data) && data.length > 0) {
        return data.map((item) => ({
          name: item.name, // 年份
          value: parseInt(item.value), // 人数
          rate: parseFloat(item.value1.replace('%', '')), // 就业率（去掉%符号）
        }))
      }
      return []
    },

    initChart() {
      const chart = this.$echarts.init(document.getElementById('newEmploymentStatus'))

      const categories = this.chartData.map((item) => item.name)
      const values = this.chartData.map((item) => item.value)
      const rates = this.chartData.map((item) => item.rate)

      // 计算两个y轴的范围,确保0刻度对齐
      const valueMax = Math.ceil(Math.max(...values, 0) / 1000) * 1000
      const valueMin = Math.ceil(Math.min(...values, 0) / 1000) * 1000
      const rateMax = Math.ceil(Math.max(...rates, 0) / 10) * 10
      const rateMin = Math.ceil(Math.min(...rates, 0) / 10) * 10

      // 计算比例,使0刻度对齐
      const valueRange = Math.max(Math.abs(valueMax), Math.abs(valueMin))
      const rateRange = Math.max(Math.abs(rateMax), Math.abs(rateMin))

      const option = {
        tooltip: {
          trigger: 'axis',
          backgroundColor: 'rgba(0, 0, 0, 0.8)',
          borderColor: '#0091FF',
          textStyle: { color: '#fff', fontSize: 26 },
          formatter: function (params) {
            let result = params[0].name + '<br/>'
            params.forEach((param) => {
              if (param.seriesName === '新增就业人数') {
                result += param.marker + param.seriesName + ': ' + param.value + '人<br/>'
              } else if (param.seriesName === '新增就业人口增长率') {
                result += param.marker + param.seriesName + ': ' + param.value + '%<br/>'
              }
            })
            return result
          },
        },
        legend: {
          data: ['新增就业人数', '新增就业人口增长率'],
          textStyle: { color: '#fff', fontSize: 24 },
          top: '5%',
        },
        grid: { left: '18%', right: '12%', top: '20%', bottom: '12%' },
        xAxis: {
          type: 'category',
          data: categories,
          axisLabel: { color: '#fff', fontSize: 24 },
          axisLine: { lineStyle: { color: '#333' } },
        },
        yAxis: [
          {
            type: 'value',
            name: '单位（人）',
            nameTextStyle: {
              color: '#fff',
              fontSize: 24,
              padding: [0, 0, 0, 10],
            },
            min: -valueRange,
            max: valueRange,
            axisLabel: {
              color: '#fff',
              fontSize: 24,
              formatter: '{value}',
            },
            axisLine: { lineStyle: { color: '#333' } },
            splitLine: { lineStyle: { color: '#333' } },
          },
          {
            type: 'value',
            name: '新增就业人口增长率',
            nameTextStyle: {
              color: '#fff',
              fontSize: 24,
              padding: [0, 80, 0, 0],
            },
            min: -rateRange,
            max: rateRange,
            axisLabel: {
              color: '#fff',
              fontSize: 24,
              formatter: '{value}%',
            },
            axisLine: { lineStyle: { color: '#333' } },
            splitLine: { show: false },
          },
        ],
        series: [
          {
            name: '新增就业人数',
            type: 'bar',
            yAxisIndex: 0,
            data: values,
            itemStyle: {
              color: '#0091FF',
              borderRadius: [4, 4, 0, 0],
            },
            barWidth: 24,
            animationDuration: 1000,
            animationEasing: 'cubicOut',
          },
          {
            name: '新增就业人口增长率',
            type: 'line',
            yAxisIndex: 1,
            data: rates,
            lineStyle: {
              color: '#FF6B35',
              width: 3,
            },
            itemStyle: {
              color: '#FF6B35',
            },
            symbol: 'circle',
            symbolSize: 8,
            animationDuration: 1500,
            animationEasing: 'cubicOut',
          },
        ],
      }
      chart.setOption(option)

      // 响应式处理
      window.addEventListener('resize', () => {
        chart.resize()
      })
    },
  },
}
</script>

<style lang="less" scoped>
.chart-item {
  flex: 1;
  background: transparent;
  border-radius: 8px;
  padding: 10px;
  border: none;

  .chart-container {
    position: relative;

    .chart {
      width: 100%;
      height: 470px;
    }
  }
}
</style>

