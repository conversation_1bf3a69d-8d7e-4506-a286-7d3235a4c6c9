<template>
  <el-dialog
    :title="name"
    :visible.sync="dialogVisible"
    width="3129px"
    top="5vh"
    :close-on-click-modal="false"
    :close-on-press-escape="true"
    custom-class="enterprise-info-dialog"
    @close="handleClose"
  >
    <!-- 弹窗头部标签页 -->
    <div class="dialog-top" slot="title">
      <span class="col-yellow" style="font-size: 68px;padding-left: 50px;">{{ name }}</span>
      <div class="tabs" v-show="isTabClick">
        <div
          class="tab_item"
          v-for="(item, index) in tabList"
          :key="index"
          @click="tabClick(index)"
          :class="tabIndex == index ? 'tab_active' : ''"
          :style="{ cursor: isTabClick ? 'pointer' : 'not-allowed' }"
        >
          <span>{{ item }}</span>
        </div>
      </div>
    </div>

    <!-- 招引清单内容 -->
    <div class="dialog-con">
      <div
        class="data-list"
        v-loading="loading"
        element-loading-text="拼命加载中"
        element-loading-spinner="el-icon-loading"
        element-loading-background="rgba(0, 0, 0, 0.8)"
      >
        <div v-show="showNull" class="nullData">暂无数据</div>
        <div
          v-show="!showNull"
          class="data-item"
          v-for="item in list"
          :key="item.tyshxydm"
          @click="openQyDetail(item, tabIndex)"
        >
          <div class="item-header">
            <div>{{ item.gsmc }}</div>
            <div class="youTxt">{{ item.fzsz }}</div>
          </div>
          <div class="item-label">
            <span v-for="label in formatLabels(item.qytsbq)" :key="label">{{ label }}</span>
          </div>
          <div class="item-con">
            <div class="item" style="width: 55%">
              <span class="icon"></span>
              <span class="col-grey">注册地址：</span>
              <span :title="item.zcdz">{{ item.zcdz || '-' }}</span>
            </div>
            <div class="item" style="width: 20%">
              <span class="icon"></span>
              <span class="col-grey">成立时间：</span>
              <span>{{ item.clrq || '-' }}</span>
            </div>
            <div class="item" style="width: 18%">
              <span class="icon"></span>
              <span class="col-grey">注册资本：</span>
              <span>{{ item.zczb || '-' }}万元</span>
            </div>
          </div>
        </div>
      </div>

      <div class="data-page">
        <el-pagination
          background
          layout="total, sizes, prev, pager, next, jumper"
          @current-change="handleCurrentChange"
          @size-change="handleSizeChange"
          :total="total"
          :page-size="limit"
          :current-page="pagenum"
        />
      </div>
    </div>
  </el-dialog>
</template>

<script>
import { getCsdnInterface1 } from '@/api/csdnIndexApi'

export default {
  name: 'EnterpriseInfoDialog',
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    dialogData: {
      type: Object,
      default: () => ({}),
    },
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible
      },
      set(value) {
        this.$emit('update:visible', value)
      },
    },
  },
  data() {
    return {
      loading: false,
      showNull: false,
      name: '-',
      list: [],
      total: 0,
      pagenum: 1,
      limit: 10,
      ssjd: null, // 所属节点
      qytsbq: null,
      sstp: null, // 所属图谱
      status: null,
      tabIndex: 0,
      tabList: ['建议招引配套企业'],
      isTabClick: true,
      dictList: {
        sfbg: '变更',
        sfdx: '吊销',
        sfsx: '失信',
        sfyxzcf: '有行政处罚',
        sfzqyjyycml: '在企业经营异常名录',
        sfyaqscxkz: '有安全生产许可证',
        sfyzdhzyh: '有重大火灾隐患',
        sfztqgzhmd: '在拖欠工资黑名单',
        sfsyzwfqy: '严重违法企业',
        sfyldbzcf: '有劳动保障处罚',
        sfzhjwfsxhmd: '在环境违法失信黑名单',
        sfyxzqzxx: '有行政强制信息',
        sfswhpscqy: '危化品生产企业',
        sfswhpysqy: '危化品运输企业',
        sfsgxjsqy: '高新技术企业信息',
        sfsxwqy: '小微企业',
        sfzjrfxqymd: '在金融风险企业名单',
        sfsczbzqy: '财政补助企业',
        sfssbfcqy: '涉爆粉尘企业',
        sfsjyghnhqqy: '金阳光惠农惠企企业',
        sfsjyghnhqxm: '金阳光惠农惠企项目',
        sfygksmlyscaqsg: '有工矿商贸领域生产安全事故',
        sfsgsqy: '规上企业',
        sfsssqy: '上市企业',
        sfsywjcyl: '五金产业链',
        sfsyfzycyl: '纺织业产业链',
        sfsygdcyl: '光电产业链',
        sfsyqccyl: '汽车产业链',
        sfsyxjzbzzycyl: '先进装备制造业产业链',
        sfsyqmpcyl: '汽摩配产业链',
      },
    }
  },
  watch: {
    visible(newVal) {
      if (newVal) {
        this.initDialog()
      }
    },
    dialogData: {
      handler(newVal) {
        if (newVal && Object.keys(newVal).length > 0) {
          this.handleDialogData(newVal)
        }
      },
      deep: true,
      immediate: true,
    },
  },
  methods: {
    initDialog() {
      // 初始化弹窗数据
      this.tabIndex = 0
      this.pagenum = 1
      this.limit = 10
      // 调用招引清单接口
      getCsdnInterface1('qyhx_ent_zs', {
        // 这里传入你需要的参数
        pagenum: 1, // 页码
        pagesize: 10, // 每页数量
        ld_name: '节点名称', // 所属节点
        // 其他可能的参数...
      })
        .then((res) => {
          console.log('招引清单数据:', res.data.data)
          // 处理返回的招引清单数据
          if (res.data.data && res.data.data.length > 0) {
            const responseData = res.data.data[0]
            if (responseData && Array.isArray(responseData.result)) {
              this.total = responseData.total || 0
              this.list = responseData.result.map((item) => ({
                id: item.id,
                gsmc: item.qymc,
                zcdz: item.zcdz,
                clrq: item.clsj,
                zczb: item.zczb,
                fzsz: `评级：${item.pf}`,
                tyshxydm: item.id ? String(item.id) : '',
                qytsbq: ['招引清单企业'],
              }))
              this.showNull = this.list.length === 0
            } else {
              this.showNull = true
              this.total = 0
              this.list = []
            }
          }
        })
        .catch((error) => {
          console.error('获取招引清单数据失败:', error)
        })
    },

    handleDialogData(data) {
      if (data.name) {
        this.name = data.name
        this.ssjd = data.ssjd
        this.sstp = data.sstp
        this.status = data.status

        if (this.visible) {
          this.getList()
        }
      }
    },

    handleClose() {
      this.$emit('update:visible', false)
      this.$emit('close')
    },

    tabClick(index) {
      if (!this.isTabClick) return
      this.tabIndex = index
      this.pagenum = 1
      this.limit = 10
      this.getList()
    },

    formatLabels(labels) {
      if (Array.isArray(labels) && labels.length > 0) {
        return labels
      }
      return ['暂无']
    },

    // API调用方法 - 获取招引清单数据
    getList() {
      this.loading = true
      this.showNull = false
      this.list = []

      // 调用招引清单接口
      getCsdnInterface1('qyhx_ent_zs', {
        pagenum: this.pagenum,
        pagesize: this.limit,
        ld_name: this.ssjd,
      })
        .then((res) => {
          console.log('招引清单数据:', res)
          this.loading = false

          // 根据你提供的数据结构处理响应
          if (res.data.data && res.data.data.length > 0) {
            const responseData = res.data.data[0]
            if (responseData && Array.isArray(responseData.result)) {
              this.total = responseData.total || 0
              this.list = responseData.result.map((item) => ({
                id: item.id,
                gsmc: item.qymc,
                zcdz: item.zcdz,
                clrq: item.clsj,
                zczb: item.zczb,
                fzsz: `评级：${item.pf}`,
                tyshxydm: item.id ? String(item.id) : '',
                qytsbq: ['招引清单企业'],
              }))
              this.showNull = this.list.length === 0
            } else {
              this.showNull = true
              this.total = 0
              this.list = []
            }
          }
        })
        .catch((error) => {
          console.error('获取招引清单数据失败:', error)
          this.loading = false
          this.showNull = true
          this.total = 0
          this.list = []
        })
    },

    openQyDetail(item, tabIndex) {
      if (!item.tyshxydm) return
      this.$emit('open-detail', { item, tabIndex })
    },

    handleCurrentChange(val) {
      this.pagenum = val
      this.getList()
    },

    handleSizeChange(val) {
      this.limit = val
      this.pagenum = 1
      this.getList()
    },
  },
}
</script>

<style lang="less" scoped>
// 滚动条样式
::-webkit-scrollbar {
  width: 6px;
  height: 1px;
}

::-webkit-scrollbar-thumb {
  border-radius: 6px;
  background: #20aeff;
  height: 8px;
}

.dialog-top {
  font-size: 50px;
  font-weight: 700;
  color: #ffffff;
  padding-left: 30px;
  height: 100px;
  line-height: 100px;
  display: flex;
  justify-content: space-between;
  position: relative;
  padding-top: 80px;
  padding-bottom: 20px;

  .col-yellow {
    background: linear-gradient(0deg, #ffcc00 0.4150390625%, #ffffff 99.5849609375%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    width: 750px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .tabs {
    display: flex;
    position: absolute;
    top: 70%;
    left: 50%;
    transform: translate(-50%, -50%);

    .tab_item {
      padding: 0 20px;
      height: 80px;
      font-size: 50px;
      font-weight: 400;
      color: #bfbcbc;
      background: #0a3b48;
      border: 2px solid #17a290;
      text-align: center;
      line-height: 80px;

      border-radius: 15px;

      &.tab_active {
        color: #ffffff;
        font-weight: 700;
        background: #0a3b48;
      }
    }
  }
}

.dialog-con {
  width: 3000px;
  height: 1550px;
  margin: 30px auto;
  box-sizing: border-box;

  .data-list {
    width: 100%;
    height: calc(100% - 100px);
    color: #fff;
    font-size: 36px;
    margin-top: 60px;
    overflow-y: auto;

    .nullData {
      font-size: 36px;
      color: #fff;
      line-height: 1460px;
      text-align: center;
      background: rgba(255, 255, 255, 0.1);
      border-radius: 8px;
    }

    .data-item {
      width: 3000px;
      height: 335px;
      background: rgba(255, 255, 255, 0.1);
      border-radius: 8px;
      margin-bottom: 30px;
      padding: 30px 50px;
      box-sizing: border-box;
      overflow-y: auto;
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        background: rgba(255, 255, 255, 0.15);
        transform: translateY(-2px);
      }
    }
  }

  .data-page {
    width: 100%;
    height: 100px;
    margin-top: 20px;
    display: flex;
    align-items: center;
    justify-content: flex-end;
  }
}

.item-header {
  font-size: 64px;
  font-weight: 700;
  display: flex;
  align-items: center;
  justify-content: space-between;

  .youTxt {
    background: linear-gradient(180deg, #ffc460 0%, #fd852e 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
}

.item-label {
  display: flex;
  margin: 30px 0 40px;
  overflow: hidden;
  white-space: nowrap;

  span {
    display: inline-block;
    height: 54px;
    line-height: 54px;
    font-size: 32px;
    background: rgba(19, 194, 194, 0.302);
    border-radius: 10px;
    padding: 0 15px;
    margin-right: 15px;
  }
}

.item-con {
  display: flex;
  justify-content: space-between;

  .item {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    line-height: 44px;

    .icon {
      display: inline-block;
      width: 8px;
      height: 34px;
      background: #bad3ff;
      border-radius: 4px;
      margin: 0 10px -3px 0;
    }

    .col-grey {
      color: #bad3ff;
    }
  }
}

.item-details {
  display: flex;
  gap: 20px;
}

.detail-item {
  display: flex;
  align-items: center;
  color: #fff;
  font-size: 14px;
}

.detail-icon {
  width: 4px;
  height: 4px;
  background: #ffd700;
  border-radius: 50%;
  margin-right: 8px;
}

.detail-label {
  color: #ccc;
  margin-right: 4px;
}

.detail-value {
  color: #fff;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.data-pagination {
  margin-top: 20px;
  text-align: center;

  /deep/ .el-pagination {
    .el-pager li {
      background: rgba(255, 255, 255, 0.1);
      color: #fff;

      &.active {
        background: #ffd700;
        color: #1e3c72;
      }
    }

    .btn-prev,
    .btn-next {
      background: rgba(255, 255, 255, 0.1);
      color: #fff;
    }

    .el-pagination__total,
    .el-pagination__jump {
      color: #fff;
    }
  }
}

.patent-content {
  .patent-charts {
    display: flex;
    gap: 20px;
    margin-bottom: 30px;
  }

  .chart-item {
    flex: 1;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    padding: 20px;
  }

  .chart-title {
    color: #fff;
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 15px;
    text-align: center;
  }

  .chart-container {
    height: 300px;
    position: relative;
  }

  .chart-wrapper {
    width: 100%;
    height: 100%;
  }

  .chart-no-data {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: #ccc;
    font-size: 14px;
  }
}

.patent-table-section {
  .section-title {
    color: #ffd700;
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 20px;
  }

  .table-wrapper {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    padding: 20px;

    /deep/ .el-table {
      background: transparent;

      th {
        background: rgba(255, 255, 255, 0.1);
        color: #fff;
        border: none;
      }

      td {
        background: transparent;
        color: #fff;
        border: none;
      }

      tr:nth-child(even) {
        background: rgba(255, 255, 255, 0.05);
      }
    }
  }
}

.talent-content {
  .talent-overview {
    display: flex;
    gap: 30px;
    margin-bottom: 30px;
    justify-content: center;
  }

  .overview-item {
    display: flex;
    align-items: center;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    padding: 20px;

    img {
      width: 60px;
      height: 60px;
      margin-right: 15px;
    }
  }

  .overview-text {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }

  .overview-name {
    color: #ccc;
    font-size: 14px;
  }

  .overview-value {
    display: flex;
    align-items: baseline;
    gap: 4px;
  }

  .value-number {
    color: #ffd700;
    font-size: 24px;
    font-weight: bold;
  }

  .value-unit {
    color: #fff;
    font-size: 14px;
  }

  .talent-charts {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
  }

  .talent-chart-item {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    padding: 20px;
  }
}

.animated.fadeIn {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.isActive {
  background: #ffd700 !important;
  color: #1e3c72 !important;
}

.popClass {
  /deep/ .el-popover {
    background: rgba(0, 0, 0, 0.8);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: #fff;
  }
}

// Element UI 组件样式覆盖
/deep/ .el-table {
  font-size: 36px;
  color: #fff;
  background-color: RGBA(3, 18, 46, 1);

  .el-table__expanded-cell {
    background-color: RGBA(3, 18, 46, 1);
  }

  &::before,
  &--border::after,
  &--group::after {
    background-color: RGBA(3, 18, 46, 1);
  }

  th.el-table__cell {
    background-color: RGBA(20, 52, 91, 1);
  }

  thead {
    color: #fff;
    height: 150px;
  }

  .cell {
    line-height: 36px;
  }

  tr {
    background-color: RGBA(6, 26, 57, 1);
    height: 150px;
  }

  &--striped .el-table__body tr.el-table__row--striped td.el-table__cell {
    background-color: RGBA(13, 38, 73, 1);
  }

  &--enable-row-hover .el-table__body tr:hover > td.el-table__cell {
    background-color: RGBA(20, 52, 91, 1);
  }

  td.el-table__cell,
  th.el-table__cell.is-leaf {
    border-bottom: 1px solid RGBA(6, 25, 55, 1);
  }
}

// 分页器样式
/deep/ .el-pagination {
  &.is-background .btn-next,
  &.is-background .btn-prev,
  &.is-background .el-pager li {
    background: #5f7b96;
    color: #fff;
    font-size: 34px;
    font-weight: normal;
    height: 60px;
    line-height: 58px;
    box-sizing: border-box;
  }

  .el-pager li.active + li {
    border-left: 1px solid transparent !important;
  }

  &.is-background .el-pager li:not(.disabled).active {
    background: #0166a6;
    border-radius: 3px;
    height: 60px;
    line-height: 60px;
    box-sizing: border-box;
  }

  .el-pager li {
    background: #5f7b96;
    padding: 0 20px;
    border: 1px solid transparent;
    box-sizing: border-box;
  }

  .btn-next .el-icon,
  .btn-prev .el-icon {
    font-size: 30px;
  }

  .btn-next,
  .btn-prev {
    width: 60px;
  }

  &__total {
    color: #fff;
    font-size: 32px !important;
    margin: 0;
    padding-right: 20px;
  }

  &__sizes .el-input .el-input__inner {
    font-size: 32px;
    background-color: transparent;
    border-color: #6f788a;
    height: 60px;
    line-height: 60px;
    color: #fff;
  }

  .el-select .el-input .el-input__inner {
    padding-right: 51px;
  }

  .el-select .el-input {
    width: 250px;
    margin: 0;
  }

  button,
  span:not([class*='suffix']) {
    height: 60px;
    line-height: 60px;
  }

  .el-input__suffix {
    right: 15px;
  }
}

// Popover 样式
/deep/ .el-popover {
  background: rgba(3, 26, 50, 0.95);
  border: 3px solid rgba(5, 145, 243, 0.5);
  color: #fff;
  font-size: 32px;

  &--plain {
    padding: 18px 0px;
  }
}

.isActive {
  background-color: rgba(5, 145, 243, 1) !important;
}

// Select 下拉框样式
/deep/ .el-select-dropdown {
  background-color: rgba(2, 16, 30, 0.9) !important;
  width: 425px;
  border: 1px solid #409eff;
  border-radius: 10px;

  &__item {
    font-size: 32px;
    background-color: transparent;
    color: #ffffff;
    border-color: #6f788a;
    margin-bottom: 10px;

    &.hover,
    &:hover {
      background-color: #254674;
    }
  }
}

/deep/ .el-select .el-input .el-select__caret {
  color: #6f788a;
  transform: rotateX(0);

  &.is-reverse {
    transform: rotateX(180deg);
  }
}

/deep/ .el-select .el-input .el-input__inner,
/deep/ .el-input .el-input__inner {
  border: 1px solid #acd8f4;
  background: #0e2951;
  font-family: Source Han Sans CN;
  font-weight: 400;
  font-size: 20px;
  color: #fefefe;
  border-radius: 10px;
  height: 55px;
}

/deep/ .el-input.is-disabled .el-input__inner {
  border: 1px solid #acd8f4;
  background: #0e2951;
  font-family: Source Han Sans CN;
  font-weight: 400;
  font-size: 20px;
  color: #fefefe;
  border-radius: 10px;
  height: 55px;
  cursor: not-allowed;
}
</style>
<style lang="less">
// 全局样式，用于覆盖 el-dialog 的默认样式
.enterprise-info-dialog {
  // background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
  background: transparent;
  background-image: url('~@/pages/rkhl/img/slsw/box_bg.png');
  background-size: 100% 100%;
  border-radius: 12px;

  .el-dialog__header {
    background: rgba(0, 0, 0, 0.2);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    padding: 0;
  }

  .el-dialog__title {
    color: #ffd700;
    font-size: 24px;
    font-weight: bold;
  }

  .el-dialog__headerbtn {
    .el-dialog__close {
      color: #fff;
      font-size: 24px;

      &:hover {
        color: #ffd700;
      }
    }
  }

  .el-dialog__body {
    padding: 20px 30px;
    max-height: 80vh;
    overflow-y: auto;
    background: transparent;
    color: #fff;
    box-sizing: border-box;
  }
}
</style>
