<template>
  <div class="chart-item">
    <SubTitle title="结婚登记情况" />
    <div class="chart-container">
      <div id="marriageRegistration" class="chart"></div>
    </div>
  </div>
</template>

<script>
import SubTitle from '@/components/SubTitle.vue'
import { getCsdnInterface1 } from '@/api/csdnIndexApi'

export default {
  name: 'MarriageRegistration',
  components: { SubTitle },
  data() {
    return {
      chartData: {
        years: [],
        valueData: []
      },
      loading: false,
    }
  },
  mounted() {
    this.fetchData()
  },
  methods: {
    async fetchData() {
      try {
        this.loading = true
        // 获取 SubTitle 的 title 属性作为 type 参数
        const titleType = '结婚登记情况'
        const response = await getCsdnInterface1('rkhl_test', { type: titleType })

        console.log('接口返回数据:', response.data)

        // 处理接口返回的数据
        if (response.data && response.data.successful && response.data.data) {
          this.chartData = this.processChartData(response.data.data)
        } else {
          // 如果接口返回空数据或失败，使用空对象
          this.chartData = {
            years: [],
            valueData: []
          }
        }
      } catch (error) {
        console.error('获取数据失败:', error)
        // 请求失败时使用空对象
        this.chartData = {
          years: [],
          valueData: []
        }
      } finally {
        this.loading = false
        this.initChart()
      }
    },

    processChartData(data) {
      // 处理结婚登记情况数据，只取value值
      if (Array.isArray(data) && data.length > 0) {
        const years = data.map(item => item.name) // 年份
        const valueData = data.map(item => parseInt(item.value) || 0) // 数据值

        return {
          years,
          valueData
        }
      }

      return {
        years: [],
        valueData: []
      }
    },

    initChart() {
      const chart = this.$echarts.init(document.getElementById('marriageRegistration'))

      // 如果没有数据，显示空图表
      if (!this.chartData.years || this.chartData.years.length === 0) {
        const option = {
          title: {
            text: '暂无数据',
            left: 'center',
            top: 'middle',
            textStyle: { color: '#fff', fontSize: 24 }
          }
        }
        chart.setOption(option)
        return
      }

      const option = {
        tooltip: {
          trigger: 'axis',
          backgroundColor: 'rgba(0, 0, 0, 0.8)',
          borderColor: '#0091FF',
          textStyle: { color: '#fff', fontSize: 24 },
          formatter: function(params) {
            let result = params[0].name + '<br/>'
            result += params[0].marker + params[0].seriesName + ': ' + params[0].value + '对<br/>'
            return result
          }
        },
        grid: { left: '15%', right: '10%', top: '15%', bottom: '12%' },
        xAxis: {
          type: 'category',
          data: this.chartData.years,
          axisLabel: { color: '#fff', fontSize: 24 },
          axisLine: { lineStyle: { color: '#333' } },
        },
        yAxis: {
          type: 'value',
          name: '单位：对',
          nameTextStyle: {
            color: '#fff',
            fontSize: 24,
            padding: [0, 0, 0, 0],
          },
          axisLabel: { color: '#fff', fontSize: 24 },
          axisLine: { lineStyle: { color: '#333' } },
          splitLine: { lineStyle: { color: '#333' } },
        },
        series: [
          {
            name: '结婚登记',
            type: 'bar',
            data: this.chartData.valueData,
            itemStyle: {
              color: '#0091FF',
              borderRadius: [4, 4, 0, 0],
            },
            barWidth: 30,
            animationDuration: 1000,
            animationEasing: 'cubicOut',
          }
        ],
      }
      chart.setOption(option)

      // 响应式处理
      window.addEventListener('resize', () => {
        chart.resize()
      })
    },
  },
}
</script>

<style lang="less" scoped>
.chart-item {
  flex: 1;
  background: transparent;
  border-radius: 8px;
  padding: 10px;
  border: none;

  .chart-container {
    position: relative;

    .chart {
      width: 100%;
      height: 470px;
    }
  }
}
</style>

