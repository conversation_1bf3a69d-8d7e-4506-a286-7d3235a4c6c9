export function setAct(val) {//科学计数法--
    if (!val) return val;
    var logo = "";
    var num = val;
    num = typeof (num) === 'string' ? num : String(num)
    if (Number(val) < 0) {
        logo = "-";
        num = val.split('-')[1];
    }
    const result = num.split("");
    let position = result.indexOf(".");
    position = position !== -1 ? position : result.length;
    while (position > 3) {
        position -= 3;
        result.splice(position, 0, ",");
    }
    return logo + result.join("")
}
// console.log(setAct(12123354564123));