<template>
  <div class="chart-item">
    <SubTitle title="实时人口排名" />
    <div style="text-align: right">
      <!-- <OptionsSwitcher v-model="selectedOption" :options="options" /> -->
    </div>

    <div class="chart-container">
      <div id="populationChangeChart" class="chart"></div>
    </div>
  </div>
</template>

<script>
import OptionsSwitcher from '@/components/OptionsSwitcher.vue'
import SubTitle from '@/components/SubTitle.vue'
import { getCsdnInterface1 } from '@/api/csdnIndexApi'

export default {
  name: 'PopulationChangeChart',
  components: {
    SubTitle,
    OptionsSwitcher,
  },
  data() {
    return {
      selectedOption: '镇街',
      options: [
        { value: '镇街', label: '镇街' },
        { value: '村社', label: '村社' },
        { value: '网格', label: '网格' },
      ],
      chartData: [],
      loading: false,
    }
  },
  mounted() {
    this.fetchData()
  },
  watch: {
    // 监听选项变化，重新获取数据
    selectedOption(newVal) {
      this.fetchData()
    }
  },
  methods: {
    // 获取实时人口排名数据
    async fetchData() {
      try {
        this.loading = true
        // 根据选择的选项获取不同的数据
        const titleType = `实时人口排名-${this.selectedOption}`
        const response = await getCsdnInterface1('qyhx_pop_ssrkpm', {
          type: titleType,
          level: this.selectedOption
        })

        console.log(`实时人口排名(${this.selectedOption})接口返回数据:`, response.data)

        // 处理接口返回的数据
        if (response.data && response.data.successful && response.data.data) {
          this.chartData = this.processChartData(response.data.data)
        } else {
        }
      } catch (error) {

      } finally {
        this.loading = false
        this.initPopulationRankingChart()
      }
    },

    // 处理图表数据
    processChartData(data) {
      // 根据实际接口返回的数据结构进行处理
      if (Array.isArray(data) && data.length > 0) {
        // 按人口数量降序排序，取前10名
        const sortedData = data
          .map(item => ({
            name: item.name || item.region || item.area || '未知区域',
            value: item.value || item.count || item.population || 0
          }))
          .sort((a, b) => b.value - a.value)
          .slice(0, 10)

        return {
          categories: sortedData.map(item => item.name),
          values: sortedData.map(item => item.value)
        }
      }

      // 如果数据格式不符合预期，返回默认数据
      return {
        categories: ['龙华街道', '民治街道', '大浪街道', '观湖街道', '福城街道', '观澜街道'],
        values: [15680, 14520, 13890, 12760, 11340, 10890]
      }
    },

    // 实时人口排名横向条形图
    initPopulationRankingChart() {
      const chart = this.$echarts.init(document.getElementById('populationChangeChart'))
      const option = {
        tooltip: {
          trigger: 'axis',
          backgroundColor: 'rgba(0, 0, 0, 0.8)',
          borderColor: '#00c0ff',
          textStyle: { color: '#fff', fontSize: 24 },
          formatter: function(params) {
            const data = params[0]
            return `${data.name}<br/>${data.seriesName}: ${data.value.toLocaleString()}人`
          }
        },
        grid: {
          left: '5%',
          right: '10%',
          top: '10%',
          bottom: '10%',
          containLabel: true
        },
        xAxis: {
          type: 'value',
          axisLabel: {
            color: '#fff',
            fontSize: 20,
            formatter: function(value) {
              return value.toLocaleString()
            }
          },
          axisLine: { lineStyle: { color: '#333' } },
          splitLine: { lineStyle: { color: '#333' } },
        },
        yAxis: {
          type: 'category',
          data: this.chartData.categories,
          axisLabel: { color: '#fff', fontSize: 20 },
          axisLine: { lineStyle: { color: '#333' } },
          axisTick: { show: false },
          inverse: true, // 倒序显示，让排名第一的在顶部
        },
        series: [
          {
            name: '人口数量',
            type: 'bar',
            data: this.chartData.values,
            barWidth: 25,
            itemStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 1,
                y2: 0,
                colorStops: [
                  { offset: 0, color: 'rgba(19, 194, 194, 0.3)' },
                  { offset: 1, color: '#13C2C2' }
                ]
              },
              borderRadius: [0, 4, 4, 0]
            },
            label: {
              show: true,
              position: 'right',
              color: '#fff',
              fontSize: 18,
              formatter: function(params) {
                return params.value.toLocaleString() + '人'
              }
            },
            animationDuration: 1000,
            animationEasing: 'cubicOut'
          },
        ],
      }
      chart.setOption(option)

      // 响应式处理
      window.addEventListener('resize', () => {
        chart.resize()
      })
    },
  },
}
</script>

<style lang="less" scoped>
.chart-item {
  flex: 1;
  background: transparent;
  border-radius: 8px;
  padding: 10px;
  border: none;

  .chart-container {
    position: relative;

    .chart-controls {
      position: absolute;
      top: 10px;
      right: 10px;
      display: flex;
      gap: 8px;
      z-index: 10;

      .control-btn {
        width: 120px;
        height: 40px;
        font-size: 24px;
        font-weight: 400;
        color: #bfbcbc;
        background: rgba(0, 74, 166, 0.3);
        border: 2px solid #215293;
        text-align: center;
        line-height: 40px;
        cursor: pointer;
        transition: all 0.3s ease;

        &.active {
          color: #ffffff;
          font-weight: 700;
          background: rgba(0, 74, 166, 0.8);
        }

        &:hover {
          background: rgba(0, 74, 166, 0.6);
          color: #ffffff;
        }
      }
    }

    .chart {
      width: 100%;
      height: 470px;
    }
  }
}
</style>
