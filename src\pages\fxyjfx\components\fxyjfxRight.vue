<!--
 * @Description: 产业链右侧组件
 * @Version: 1.0
 * @Autor: wjb
 * @Date: 2025-09-18 14:08:20
 * @LastEditors: wjb
 * @LastEditTime: 2025-09-28 15:53:19
-->
<template>
  <div class="fxyjfx_right">
    <MainTitle title="综合人口活力分析报告" size="large">
      <template #right>
        <el-button class="export_btn" @click="exportData">生成报告</el-button>
      </template>
    </MainTitle>
    <div class="right3">
      <CommonTable
        :height="'410px'"
        :tableData="tableData2"
        style="margin-top: 16px"
        :showIndex="true"
        @infoClick="infoClick"
        :tooltip="false"
      ></CommonTable>
    </div>
    <MainTitle title="重点区域实时人口变化情况分析报告" size="large" />
    <div class="right1">
      <div class="filter_box">
        <el-input placeholder="请输入内容" v-model="yearInput" class="search_input" style="width: 648px">
          <el-button slot="append" class="search_btn" @click="getData1">搜索</el-button>
        </el-input>
      </div>
      <CommonTable
        :height="'410px'"
        :tableData="tableData"
        style="margin-top: 16px"
        :showIndex="true"
        @infoClick="infoClick"
        :tooltip="false"
      ></CommonTable>
    </div>
    <MainTitle title="定期人口活力分析报告" size="large" />
    <div class="right2">
      <div class="filter_box">
        <el-input placeholder="请输入内容" v-model="monthInput" class="search_input" style="width: 648px">
          <el-button slot="append" class="search_btn" @click="getData2">搜索</el-button>
        </el-input>
      </div>

      <CommonTable
        :height="'410px'"
        :tableData="tableData1"
        style="margin-top: 16px"
        :showIndex="true"
        @infoClick="infoClick"
        :tooltip="false"
      ></CommonTable>
    </div>
    <Commondialog :title="'报告生成'" :dialogFlag="visible" @close="visible = false" :dialog-width="'1800px'">
      <exportReport @close="visible = false" @addSuccess="addSuccess" />
    </Commondialog>
  </div>
</template>

<script>
import { getCsdnInterface1, geReportList } from '@/api/csdnIndexApi'
import { getEncode64 } from '@/utils/index.js'
import MainTitle from '@/components/MainTitle.vue'
import CommonTable from './commonTable.vue'
import exportReport from './exportReport.vue'
import Commondialog from '@/components/Commondialog'

export default {
  name: 'FxyjfxRight',
  components: { MainTitle, CommonTable, exportReport, Commondialog },
  data() {
    return {
      //城市活力分析报告月报
      monthInput: '',
      tableData: {
        thead: [
          { label: '年报名称', property: 'reportName', width: 1000, align: 'center' },
          { label: '操作', property: 'handle', width: 360, align: 'center' },
        ],
        tbody: [],
      },
      //城市活力分析报告年报
      yearInput: '',
      tableData1: {
        thead: [
          { label: '月报名称', property: 'reportName', width: 1000, align: 'center' },
          { label: '操作', property: 'handle', width: 400, align: 'center' },
        ],
        tbody: [],
      },
      //活力专题分析报告
      tableData2: {
        thead: [
          { label: '报告名称', property: 'reportName', width: 1000, align: 'center' },
          { label: '操作', property: 'handle', width: 360, align: 'center' },
        ],
        tbody: [],
      },
      //生成报告弹框
      visible: false,
    }
  },

  created() {
    this.getData()
    this.getData1()
    this.getData2()
  },

  mounted() {},
  methods: {
    addSuccess() {
      this.visible = false
      this.getData()
    },
    getData() {
      geReportList().then((res) => {
        console.log(res)
        this.tableData2.tbody = res.data.rows
      })
    },
    getData1() {
      getCsdnInterface1('qyhx_pop_report', { reportType: '年报', reportName: this.yearInput }).then((res) => {
        this.tableData.tbody = res.data.data
      })
    },
    getData2() {
      getCsdnInterface1('qyhx_pop_report', { reportType: '月报', reportName: this.monthInput }).then((res) => {
        this.tableData1.tbody = res.data.data
      })
    },
    exportData() {
      this.visible = true
    },
    async infoClick(item, index) {
      console.log(item, index)
      if (item.reportUrl) {
        if (index == 1) {
          window.open(
            'http://223.76.96.197:8082/KKfileView/onlinePreview?url=' + encodeURIComponent(getEncode64(item.reportUrl)),
            '_blank'
          )
        } else {
          const fileName = item.name || this.getFileNameFromUrl(item.reportUrl) || '下载文件'
          await this.downloadFile(item.reportUrl, fileName)
        }
      } else {
        this.$message.warning('暂无文件')
      }
    },

    getFileNameFromUrl(url) {
      try {
        const decoded = decodeURIComponent(url)
        const parts = decoded.split('?')[0].split('/')
        return parts[parts.length - 1] || ''
      } catch (e) {
        return ''
      }
    },

    async downloadFile(url, fileName) {
      const a = document.createElement('a')
      a.style.display = 'none'
      a.href = url
      a.setAttribute('download', fileName)
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
    },
  },

  beforeDestroy() {},
}
</script>

<style scoped lang="less">
.fxyjfx_right {
  width: 1550px;
  margin-right: 50px;
  .right1 {
    height: 550px;
  }
  .right2 {
    height: 550px;
  }
  .right3 {
    height: 460px;
  }
  .filter_box {
    display: flex;
    align-content: center;
    align-items: center;
    margin-top: 24px;
    /deep/.el-select {
      width: 210px;
      height: 75px;
      margin-left: 40px;
      background: rgba(0, 61, 61, 0.8);
      border-radius: 10px 10px 10px 10px;
      border: 2px solid #1a93b5;
      box-sizing: border-box;
      .el-input__inner {
        font-size: 36px !important;
        border: none !important;
        background: transparent;
        height: 75px;
        line-height: 75px !important;
      }
      .el-select__caret {
        font-size: 36px !important;
      }
      .el-input__icon {
        line-height: 75px !important;
        width: 40px !important;
      }
    }
    /deep/.search_input {
      height: 75px;
      // margin-left: 40px;
      background: rgba(0, 61, 61, 0.8);
      border-radius: 10px 10px 10px 10px;
      border: 2px solid #1a93b5;
      .el-input__inner {
        font-size: 36px !important;
        border: none !important;
        background: transparent;
        height: 75px;
        line-height: 75px !important;
      }
      .el-input-group__append {
        border: none !important;
        background: transparent;
      }
      .search_btn {
        width: 125px;
        height: 75px;
        background: linear-gradient(360deg, #003e4f 0%, #38c2f0 100%);
        border: none;
        font-size: 33px;
        color: #fefefe;
        line-height: 48px;
      }
    }
  }
}
.export_btn {
  width: 180px;
  height: 75px;
  background: linear-gradient(360deg, #003e4f 0%, #38c2f0 100%);
  border: none;
  font-size: 33px;
  color: #fefefe;
  border-radius: 8px;
  margin-top: 14px;
}
</style>
<style lang="less">
.el-select-dropdown__item {
  font-size: 28px !important;
}
</style>