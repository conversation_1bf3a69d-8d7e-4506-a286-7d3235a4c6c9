<template>
  <div class="chart-item">
    <SubTitle title="外来人口来源地" />
    <div class="chart-container">
      <div id="migrantPopulationFeatures" class="chart"></div>
    </div>
  </div>
</template>

<script>
import SubTitle from '@/components/SubTitle.vue'
import { getOriginCity } from '@/api/rkhl/rkhl'

export default {
  name: 'MigrantPopulationFeatures',
  components: { SubTitle },
  data() {
    return {
      chartData: [],
      loading: false,
    }
  },
  mounted() {
    this.fetchData()
  },
  methods: {
    async fetchData() {
      try {
        this.loading = true
        const response = await getOriginCity({
          code: '420900',
          limit: 10,
          isShowLocal: false,
        })

        // 处理接口返回的数据
        if (response.data.code === 200 && response.data.data && response.data.data.top10) {
          this.chartData = this.processChartData(response.data.data.top10)
        } else {
          // 如果接口返回空数据或失败，使用空数组
          this.chartData = []
        }
      } catch (error) {
        console.error('获取数据失败:', error)
        // 请求失败时使用空数组
        this.chartData = []
      } finally {
        this.loading = false
        this.initChart()
      }
    },

    processChartData(top10Data) {
      console.log('处理数据', top10Data)
      // 处理 top10 数组数据，转换为饼图所需格式
      if (Array.isArray(top10Data) && top10Data.length > 0) {
        return top10Data.map((item) => ({
          name: item.name,
          value: item.rate,
        }))
      }

      // 如果数据格式不符合预期，返回空数组
      return []
    },

    initChart() {
      const chart = this.$echarts.init(document.getElementById('migrantPopulationFeatures'))

      const option = {
        tooltip: {
          trigger: 'item',
          backgroundColor: 'rgba(0, 0, 0, 0.8)',
          borderColor: '#E77930',
          textStyle: { color: '#fff', fontSize: 24 },
          formatter: '{b}: {d}%',
        },
        legend: {
          show: false,
          orient: 'vertical',
          right: '5%',
          top: 'center',
          textStyle: {
            color: '#fff',
            fontSize: 20,
          },
          itemWidth: 20,
          itemHeight: 14,
        },
        series: [
          {
            name: '来源城市',
            type: 'pie',
            radius: ['40%', '70%'],
            center: ['50%', '50%'],
            avoidLabelOverlap: false,
            itemStyle: {
              borderRadius: 10,
              borderColor: '#fff',
              borderWidth: 2,
            },
            label: {
              show: false,
              fontSize: 18,
              color: '#fff',
              formatter: '{b}: {d}%',
            },
            emphasis: {
              label: {
                show: false,
                fontSize: 22,
                fontWeight: 'bold',
              },
            },
            labelLine: {
              show: false,
              lineStyle: {
                color: '#fff',
              },
            },
            data: this.chartData,
            animationDuration: 1000,
            animationEasing: 'cubicOut',
          },
        ],
        color: [
          '#E77930',
          '#FF9F5A',
          '#FFB87A',
          '#FFC999',
          '#FFD9B8',
          '#5470C6',
          '#91CC75',
          '#FAC858',
          '#EE6666',
          '#73C0DE',
        ],
      }
      chart.setOption(option)

      // 响应式处理
      window.addEventListener('resize', () => {
        chart.resize()
      })
      tools.loopShowTooltip(chart, option, {
        loopSeries: true,
      })
    },
  },
}
</script>

<style lang="less" scoped>
.chart-item {
  flex: 1;
  background: transparent;
  border-radius: 8px;
  padding: 10px;
  border: none;

  .chart-container {
    position: relative;

    .chart {
      width: 100%;
      height: 450px;
    }
  }
}
</style>
