const { defineConfig } = require('@vue/cli-service')
const path = require('path')
const resolve = (dir) => {
  return path.join(__dirname, dir)
}
const CompressionWebpackPlugin = require('compression-webpack-plugin')
const isProd = process.env.NODE_ENV === 'production'

module.exports = defineConfig({
  transpileDependencies: true,
  outputDir: process.env.NODE_ENV === 'production' ? 'dist' : 'stage',
  chainWebpack: (config) => {
    config.resolve.alias.set('@$', resolve('src'))
  },
  configureWebpack: {
    resolve: {
      alias: {
        '@': resolve('src'),
      },
    },
    plugins: isProd
      ? [
          new CompressionWebpackPlugin({
            test: /\.js$|\.html$|\.css$/u,
            threshold: 4096, // 超过 4kb 压缩
          }),
        ]
      : [],
  },
  pages: {
    index: {
      // page 的入口
      entry: 'src/main.js',
      // 模板来源
      template: 'public/index.html',
      // 在 dist/index.html 的输出
      filename: 'index.html',
      // 当使用 title 选项时，
      // template 中的 title 标签需要是 <title><%= htmlWebpackPlugin.options.title %></title>
      title: '安陆智能体驾驶舱项目',
      // 在这个页面中包含的块，默认情况下会包含
      // 提取出来的通用 chunk 和 vendor chunk。
      chunks: ['chunk-vendors', 'index'],
    },
  },
  publicPath: './',

  devServer: {
    host: '0.0.0.0',
    port: '8080',
    allowedHosts: ['all'],
    client: {
      overlay: false, // 将 overlay 设置为 false 来禁用错误遮罩层
    },
    proxy: {
      '/csdnMap': {
        changeOrigin: true,
        secure: false,
        target: 'https://csdn.dsjj.jinhua.gov.cn:9601',
        pathRewrite: {
          '^/csdnMap': 'https://csdn.dsjj.jinhua.gov.cn:9601',
        },
        onProxyRes(proxyRes, req, res) {
          const realUrl = req.url || '' // 真实请求网址
          console.log(realUrl) // 在终端显示
          proxyRes.headers['A-Real-Url'] = realUrl // 添加响应标头(A-Real-Url为自定义命名)，在浏览器中显示
        },
      },
      '/alu-api': {
        // target: 'http://***************:9002/alu-api', //测试
        target: "http://*************:8082/alu-api", //线上
        // target: "http://*************:9302/alu-api", //盛铭
        changeOrigin: true,
        pathRewrite: {
          // '^/alu-api': 'http://***************:9002',
          '^/alu-api': "http://*************:8082/",
          // '^/alu-api': "http://*************:9302/",
        },
        onProxyRes(proxyRes, req, res) {
          const realUrl = req.url || '' // 真实请求网址
          console.log(realUrl) // 在终端显示
          proxyRes.headers['A-Real-Url'] = realUrl // 添加响应标头(A-Real-Url为自定义命名)，在浏览器中显示
        },
      },
    },
  },
})
