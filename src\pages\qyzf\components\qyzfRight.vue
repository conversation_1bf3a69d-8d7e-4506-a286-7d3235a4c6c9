<template>
  <div class="qyzf-right">
    <MainTitle title="新质生产力" size="large" />
    <div class="xzscl-con">
      <div class="qyzfright-con-item">
        <SubTitle title="创新企业" />
        <div id="chart01" style="width: 100%; height: 550px"></div>
      </div>
      <div class="qyzfright-con-item">
        <SubTitle title="高素养人才" />
        <div class="s-flex">
          <div id="chart02" style="width: 244px; height: 550px; z-index: 10"></div>
          <div class="chart-detail" v-if="chartData2 && chartData2.length > 0">
            <div class="detail-top">{{ chartData2[0].name }}</div>
            <div class="detail-con">
              <span style="color: #9bd2ff">总人数：</span>
              <span>{{ chartData2[0].value }}人</span>
            </div>
            <div class="detail-top mt_1">{{ chartData2[1].name }}</div>
            <div class="detail-con mb_1">
              <span style="color: #9bd2ff">总人数：</span>
              <span>{{ chartData2[1].value }}人</span>
            </div>
            <div class="detail-top">{{ chartData2[2].name }}</div>
            <div class="detail-con">
              <span style="color: #9bd2ff">总人数：</span>
              <span>{{ chartData2[2].value }}人</span>
            </div>
          </div>
        </div>
        <img class="qyzfright-pie_bg" src="@/pages/qyzf/img/pie_bg.png" alt="" width="200" />
      </div>
      <div class="qyzfright-con-item">
        <SubTitle title="研究与开发经费支出" />
        <div id="zhuanliqushi" style="width: 100%; height: 550px"></div>
      </div>
    </div>
    <div style="margin-top: 50px">
      <MainTitle title="助企纾困情况" size="large" />
      <div class="zqyk-container">
        <!-- 上层大框 -->
        <div class="zqyk-big-box">
          <div class="zqyk-big-content">
            <div class="zqyk-icon">
              <img src="@/pages/qyzf/img/zqyk/zqyk_icon.png" alt="" />
            </div>
            <div class="zqyk-data-grid">
              <div class="zqyk-data-item" v-for="(item, index) in lists.slice(1, 5)" :key="index">
                <div class="data-name">{{ item.name }}</div>
                <div class="data-value-box">
                  <div class="data-value s-c-blue-gradient">{{ item.value }}</div>
                  <div class="data-unit s-c-blue-gradient">{{ item.unit }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 下层三个小框 -->
        <div class="zqyk-small-boxes">
          <div class="zqyk-small-box" v-for="(item, index) in lists.slice(5, 8)" :key="index">
            <div class="small-box-content">
              <div class="small-data-name">{{ item.name }}</div>
              <div class="small-data-value-box">
                <div class="small-data-value s-c-yellow-gradient">{{ item.value }}</div>
                <div class="small-data-unit s-c-yellow-gradient">{{ item.unit }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getCsdnInterface1 } from '@/api/csdnIndexApi'
import { getEntClaim } from '@/api/qyzf/qyhx'
import MainTitle from '@/components/MainTitle.vue'
import SubTitle from '@/components/SubTitle.vue'

export default {
  name: 'UrbanSignsRight',
  components: { MainTitle, SubTitle },
  data() {
    return {
      chartData1: [],
      chartData2: [],
      lists: [
        {
          name: '企业诉求数',
          value: '-',
          unit: '个',
        },
        {
          name: '已处置诉求数',
          value: '-',
          unit: '个',
        },
        {
          name: '诉求处置率',
          value: '-',
          unit: '%',
        },
        {
          name: '涉及企业数',
          value: '-',
          unit: '家',
        },
        {
          name: '企业咨询数',
          value: '-',
          unit: '个',
        },
        {
          name: '企业建议数',
          value: '-',
          unit: '个',
        },
        {
          name: '企业投诉数',
          value: '-',
          unit: '个',
        },
        {
          name: '增值诉求数',
          value: '-',
          unit: '个',
        },
      ],
    }
  },
  mounted() {
    this.initFun()
    this.initApi()
    this.getData1()
    this.initpart3()
    this.initChart1()
    this.initChart2()
  },
  methods: {
    getData1() {
      getCsdnInterface1('qyhx_gszrc_al').then((res) => {
        this.chartData2 = res.data.data
        let imgUrl2 = require('@/pages/qyzf/img/people.png')
        this.getChart2('chart02', this.chartData2, imgUrl2)
      })
    },
    initChart2() {
      let imgUrl2 = require('@/pages/qyzf/img/education.png')
      let data = [
        { name: '专家1', value: 50 },
        { name: '专家2', value: 80 },
        { name: '专家3', value: 50 },
        { name: '专家4', value: 50 },
        { name: '专家5', value: 50 },
      ]
      this.getChart041('cyjj-chart', data, imgUrl2)
    },
    initChart1() {
      let imgUrl2 = require('@/pages/qyzf/img/home.png')
      let data = [
        { name: '服务机构1', value: 50 },
        { name: '服务机构2', value: 80 },
        { name: '服务机构3', value: 50 },
        { name: '服务机构4', value: 50 },
        { name: '服务机构5', value: 50 },
      ]
      this.getChart041('chart041', data, imgUrl2)
    },
    initApi() {
      let that = this
      getEntClaim().then((res) => {
        let resdata = res.data.data
        // 上层大框数据映射
        this.lists[0].value = resdata.sqNum // 企业诉求数
        this.lists[1].value = resdata.zxNum // 企业咨询数
        this.lists[2].value = resdata.jyNum // 企业建议数
        this.lists[3].value = resdata.tsNum // 企业投诉数
        this.lists[4].value = resdata.zzsqNum // 增值诉求数
        // 下层小框数据映射
        this.lists[5].value = resdata.czl // 诉求处置率
        this.lists[6].value = resdata.yclNum // 已处置诉求数
        this.lists[7].value = resdata.sjqyNum // 涉及企业数
      })
      getCsdnInterface1('rkhl_test', { type: 'R&D经费支出' }).then((res) => {
        let resdata = res.data.data
        that.getChart3('zhuanliqushi', resdata)
      })
    },
    barLineCharts2(id, data) {
      let myChart = echarts.init(document.getElementById(id))

      // 示例数据结构，如果传入的data为空或格式不符，使用默认数据
      const defaultData = {
        categories: ['2025-04', '2025-05', '2025-06', '2025-07', '2025-08', '2025-09'],
        series: [
          { name: '法务', data: [300, 400, 600, 400, 300, 400], color: '#4A90E2' },
          { name: '财务', data: [200, 500, 300, 700, 400, 500], color: '#50C878' },
          { name: '技术', data: [500, 600, 900, 700, 800, 600], color: '#FFB347' },
        ],
      }

      // 如果没有传入数据或数据格式不正确，使用默认数据
      const chartData = data && data.categories && data.series ? data : defaultData

      let option = {
        grid: {
          top: '12%',
          bottom: '0%',
          right: '0%',
          left: '5%',
          containLabel: true,
        },
        legend: {
          data: chartData.series.map((item) => item.name),
          top: '5%',
          right: '5%',
          textStyle: {
            color: '#fff',
            fontSize: 28,
          },
          itemWidth: 24,
          itemHeight: 24,
        },
        xAxis: {
          type: 'category',
          data: chartData.categories,
          axisLabel: {
            color: '#fff',
            fontSize: 24,
            rotate: 0,
          },
          axisLine: {
            lineStyle: { color: '#333' },
          },
          axisTick: {
            show: false,
          },
        },
        yAxis: {
          type: 'value',
          name: '单位 (次)',
          nameTextStyle: {
            color: '#fff',
            fontSize: 24,
            padding: [0, 0, 0, 0],
          },
          axisLabel: {
            color: '#fff',
            fontSize: 24,
          },
          axisLine: {
            lineStyle: { color: '#333' },
          },
          splitLine: {
            lineStyle: {
              color: '#333',
              type: 'dashed',
            },
          },
          max: 2000,
        },
        tooltip: {
          trigger: 'axis',
          borderWidth: 0,
          backgroundColor: 'rgba(0, 0, 0, 0.8)',
          axisPointer: {
            type: 'shadow',
          },
          textStyle: {
            color: 'white',
            fontSize: 24,
          },
          formatter: function (params) {
            let result = params[0].name + '<br/>'
            params.forEach((param) => {
              result += param.marker + param.seriesName + ': ' + param.value + '次<br/>'
            })
            return result
          },
        },
        series: chartData.series.map((seriesItem) => ({
          name: seriesItem.name,
          type: 'bar',
          stack: 'total',
          data: seriesItem.data,
          barWidth: '40%',
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              {
                offset: 0,
                color: seriesItem.color,
              },
              {
                offset: 1,
                color: seriesItem.color + '00', // 添加透明度00使其完全透明
              },
            ]),
            borderRadius: [0, 0, 0, 0],
          },
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)',
            },
          },
        })),
      }

      myChart.setOption(option, true)
      myChart.on('click', (params) => {
        console.log('点击了:', params)
      })

      // 如果需要循环显示tooltip，保留原有功能
      if (typeof tools !== 'undefined' && tools.loopShowTooltip) {
        tools.loopShowTooltip(myChart, option, { loopSeries: true })
      }
    },
    initpart3() {
      let data = []
      this.barLineCharts2('chart061', data)
    },
    async initFun() {
      let that = this
      getCsdnInterface1('qyhx_ent_cxqy').then((res) => {
        let resdata = res.data.data.map((item) => {
          return { name: item.type, value: item.value }
        })
        this.getChart1('chart01', resdata)
      })
    },
    getChart1(id, chartData) {
      let myChart = echarts.init(document.getElementById(id))
      let xData = chartData.map((item) => {
        return item.name
      })
      let yData = chartData.map((item) => {
        return item.value
      })
      let option = {
        tooltip: {
          trigger: 'item',
          borderWidth: 0,
          backgroundColor: 'rgba(51, 51, 51, 0.7)',
          textStyle: {
            color: 'white',
            fontSize: '28',
          },
        },
        grid: {
          top: '6%',
          bottom: '-10%',
          left: '5%',
          right: '2%',
          containLabel: true,
        },
        xAxis: {
          show: false,
          max: 100,
        },
        yAxis: [
          {
            inverse: true,
            data: xData,
            axisLine: {
              show: false,
            },
            splitLine: {
              show: false,
            },
            axisTick: {
              show: false,
            },
            axisLabel: {
              show: false,
            },
          },
          {
            inverse: true,
            data: yData,
            axisLine: {
              show: false,
            },
            splitLine: {
              show: false,
            },
            axisTick: {
              show: false,
            },
            axisLabel: {
              interval: 0,
              shadowOffsetX: '-20px',
              color: '#FD852E',
              align: 'right',
              verticalAlign: 'bottom',
              lineHeight: 50,
              fontSize: 36,
              formatter: function (value, index) {
                return value + '家'
              },
            },
          },
        ],
        series: [
          {
            name: '',
            type: 'bar',
            showBackground: true,
            barBorderRadius: 30,
            yAxisIndex: 0,
            data: chartData.map((item) => {
              return item.value > 100 ? 100 : item.value
            }),
            barWidth: 10,
            itemStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(
                  0,
                  0,
                  1,
                  0,
                  [
                    {
                      offset: 0,
                      color: 'rgba(19,194,194,0.1)',
                    },
                    {
                      offset: 1,
                      color: '#13C2C2',
                    },
                  ],
                  false
                ),
                barBorderRadius: 10,
              },
              barBorderRadius: 4,
            },
            label: {
              normal: {
                color: '#DCEFFF',
                show: true,
                position: [0, '-40px'],
                textStyle: {
                  fontSize: 32,
                },
                formatter: function (a, b) {
                  const name = a.name || ''
                  if (name.length > 8) {
                    return name.substring(0, 8) + '...'
                  }
                  return name
                },
              },
            },
          },
        ],
      }

      myChart.setOption(option)
      myChart.getZr().on('mousemove', (param) => {
        myChart.getZr().setCursorStyle('default')
      })
    },
    getChart2(id, chartData, imgUrl2) {
      let myChart = echarts.init(document.getElementById(id))
      const option = {
        tooltip: {
          trigger: 'item',
          borderWidth: 0,
          backgroundColor: 'rgba(51, 51, 51, 0.7)',
          textStyle: {
            color: 'white',
            fontSize: '28',
          },
        },
        legend: {
          orient: 'vertical',
          left: '10%',
          top: '70%',
          icon: 'circle',
          itemGap: 20,
          textStyle: {
            color: '#D6E7F9',
            fontSize: 28,
            rich: {
              percent: {
                color: '#ffd79b',
                fontSize: 28,
              },
            },
          },
          formatter: function (name) {
            var data = option.series[0].data //获取series中的data
            var total = 0
            var tarValue
            for (var i = 0, l = data.length; i < l; i++) {
              total += Number(data[i].value)
              if (data[i].name == name) {
                tarValue = data[i].value
              }
            }
            var p = (tarValue / total) * 100
            return name
            // return name + ' ' + '{percent|' + tarValue + '人}' + ' '
          },
        },
        graphic: [
          // {
          //   z: 4,
          //   type: "image",
          //   id: "logo1",
          //   left: "4.5%",
          //   top: "26%",
          //   z: -10,
          //   bounding: "raw",
          //   rotation: 0, //旋转
          //   origin: [50, 50], //中心点
          //   scale: [0.8, 0.8], //缩放
          //   style: {
          //     image: imgUrl1,
          //     opacity: 1,
          //   },
          // },
          {
            z: 5,
            type: 'image',
            id: 'logo2',
            left: '35%',
            top: '32.5%',
            z: -10,
            bounding: 'raw',
            rotation: 0, //旋转
            origin: [50, 50], //中心点
            scale: [1, 1], //缩放
            style: {
              image: imgUrl2,
              opacity: 1,
            },
          },
        ],
        series: [
          {
            name: '',
            type: 'pie',
            radius: ['60%', '70%'],
            center: ['50%', '40%'],
            itemStyle: {
              normal: {
                borderColor: '#0A1934',
                // borderWidth: 10
              },
            },
            label: {
              show: false,
            },
            data: chartData,
          },
        ],
      }
      myChart.setOption(option)
      tools.loopShowTooltip(myChart, option, {
        loopSeries: true,
      })
    },
    getChart041(id, chartData, imgUrl2) {
      let myChart = echarts.init(document.getElementById(id))

      // 定义颜色数组，更接近图片中的效果
      const colors = [
        {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 1,
          y2: 1,
          colorStops: [
            { offset: 0, color: '#5B9BD5' },
            { offset: 1, color: '#4472C4' },
          ],
        },
        {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 1,
          y2: 1,
          colorStops: [
            { offset: 0, color: '#70AD47' },
            { offset: 1, color: '#548235' },
          ],
        },
        {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 1,
          y2: 1,
          colorStops: [
            { offset: 0, color: '#FFC000' },
            { offset: 1, color: '#D68910' },
          ],
        },
        {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 1,
          y2: 1,
          colorStops: [
            { offset: 0, color: '#C55A5A' },
            { offset: 1, color: '#A04747' },
          ],
        },
        {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 1,
          y2: 1,
          colorStops: [
            { offset: 0, color: '#44C7C7' },
            { offset: 1, color: '#2E8B8B' },
          ],
        },
      ]

      const option = {
        animation: true,
        animationDuration: 2000,
        animationEasing: 'cubicOut',
        tooltip: {
          trigger: 'item',
          borderWidth: 0,
          backgroundColor: 'rgba(0, 0, 0, 0.8)',
          borderRadius: 8,
          textStyle: {
            color: 'white',
            fontSize: '24',
          },
          formatter: function (params) {
            return (
              '<div style="padding: 8px;">' +
              '<div style="font-size: 26px; font-weight: bold; margin-bottom: 5px;">' +
              params.name +
              '</div>' +
              '<div style="font-size: 24px;">对接次数: <span style="color: #FFC000;">' +
              params.value +
              '次</span></div>' +
              '<div style="font-size: 22px; color: #ccc;">占比: ' +
              params.percent +
              '%</div>' +
              '</div>'
            )
          },
        },
        legend: [
          {
            orient: 'horizontal',
            left: 'center',
            bottom: '0',
            icon: 'circle',
            itemGap: 20,
            itemWidth: 16,
            itemHeight: 16,
            textStyle: {
              color: '#FFFFFF',
              fontSize: 24,
              rich: {
                name: {
                  color: '#FFFFFF',
                  fontSize: 24,
                  width: 140,
                  align: 'left',
                },
                value: {
                  color: '#FFFFFF',
                  fontSize: 24,
                  fontWeight: 'bold',
                },
              },
            },
            formatter: function (name) {
              let tarValue = 0
              chartData.forEach((item) => {
                if (item.name === name) {
                  tarValue = item.value
                }
              })
              return '{name|' + name + '} {value|' + tarValue + '次}'
            },
            data: chartData,
          },
        ],
        graphic: [
          {
            type: 'image',
            id: 'centerIcon',
            left: '43%',
            top: '32%',
            z: 10,
            bounding: 'raw',
            style: {
              image: imgUrl2,
              width: 68,
              height: 68,
              opacity: 0.8,
            },
          },
        ],
        series: [
          {
            name: '服务机构对接次数',
            type: 'pie',
            radius: ['50%', '60%'],
            center: ['48%', '38%'],
            avoidLabelOverlap: false,
            itemStyle: {
              borderRadius: 0,
              borderColor: 'rgba(10, 25, 52, 0.8)',
              borderWidth: 3,
            },
            label: {
              show: false,
            },
            labelLine: {
              show: false,
            },
            emphasis: {
              label: {
                show: false,
              },
              itemStyle: {
                shadowBlur: 15,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.3)',
                scale: 1.05,
              },
            },
            data: chartData.map((item, index) => ({
              ...item,
              itemStyle: {
                color: colors[index % colors.length],
              },
            })),
          },
        ],
      }

      myChart.setOption(option)
      tools.loopShowTooltip(myChart, option, {
        loopSeries: true,
      })
    },
    getChart3(id, chartData) {
      let myChart = echarts.init(document.getElementById(id))
      let xData = [],
        yData = []
      chartData.forEach((item) => {
        xData.push(item.name)
        yData.push(item.value)
      })
      let option = {
        tooltip: {
          trigger: 'axis',
          backgroundColor: 'rgba(51, 51, 51, 0.7)',
          borderWidth: 0,
          axisPointer: {
            type: 'shadow',
          },
          textStyle: {
            color: 'white',
            fontSize: '32',
          },
        },
        grid: {
          left: '12%',
          right: '2%',
          top: '15%',
          bottom: '10%',
          // containLabel: true,
        },
        // legend: {
        //   top: '2%',
        //   left: 'center',
        //   textStyle: {
        //     color: '#CFD7E5',
        //     fontSize: 28,
        //   },
        // },
        xAxis: [
          {
            type: 'category',
            data: xData,
            axisLine: {
              lineStyle: {
                color: 'rgb(119,179,241,.4)', // 颜色
                width: 1, // 粗细
              },
            },
            axisTick: {
              show: false,
            },
            axisLabel: {
              padding: 10,
              textStyle: {
                color: '#fff',
                fontSize: 30,
              },
            },
          },
        ],
        yAxis: [
          {
            name: '单位：亿元',
            type: 'value',
            nameTextStyle: {
              fontSize: 28,
              color: '#fff',
              padding: [5, -30, 0, 0],
            },
            splitLine: {
              lineStyle: {
                color: 'rgb(119,179,241,.4)',
              },
            },
            axisLabel: {
              textStyle: {
                fontSize: 28,
                color: '#fff',
              },
            },
          },
        ],
        series: [
          {
            name: '经费支出',
            type: 'line',
            symbol: 'circle',
            symbolSize: 8,
            itemStyle: {
              normal: {
                color: '#FFC460',
                lineStyle: {
                  color: '#FFC460',
                  width: 1,
                },
                areaStyle: {
                  color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                    {
                      offset: 0,
                      color: 'rgba(177,113,46,0.5)',
                    },
                    {
                      offset: 1,
                      color: 'rgba(177,113,46,0)',
                    },
                  ]),
                },
              },
            },
            data: yData,
          },
        ],
      }
      myChart.setOption(option)
      tools.loopShowTooltip(myChart, option, {
        loopSeries: true,
      })
      myChart.getZr().on('mousemove', (param) => {
        myChart.getZr().setCursorStyle('default')
      })
    },
  },
  computed: {},
  watch: {},
}
</script>

<style scoped lang="less">
.qyzf-right {
  width: 1550px;
}
/* 标题 */
.first-title {
  position: relative;
  width: 100%;
  height: 95px;
  background-image: url('@/pages/qyzf/img/first-title.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  padding-left: 100px;
  padding-right: 50px;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: 20px 0;
  line-height: 110px;
  font-size: 45px;
  color: #dcefff;
  letter-spacing: 2px;
  font-weight: 700;
}
.first-title > i {
  position: absolute;
  width: 1062px;
  height: 90px;
  display: inline-block;
  background: url('@/pages/qyzf/img/line.png');
  background-size: 100%;
  animation-name: animateLine;
  animation-duration: 10s;
  animation-iteration-count: infinite;
  /* animation-direction:alternate; */
  /* animation-delay */
}
@keyframes animateLine {
  0% {
    left: -20%;
    opacity: 0.4;
  }
  50% {
    opacity: 1;
  }
  100% {
    left: 70%;
    opacity: 0.2;
  }
}
.second-title {
  width: 100%;
  height: 90px;
  font-size: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #dcefff;
  background-image: url('@/pages/qyzf/img/second-title.png');
  background-size: 100% 100%;
  /* background: linear-gradient( 268deg, rgba(0,121,227,0) 0%, rgba(28,148,249,0.5882) 51%, rgba(8,17,26,0) 100%); */
  border-radius: 0px 0px 0px 0px;
  margin-bottom: 20px;
  position: relative;
}
/* 鼠标禁用事件 */
.mouse-no {
  pointer-events: none;
}
.mouse-pointer {
  cursor: pointer;
}
.mouse-not {
  /* cursor: not-allowed; */
  cursor: default;
}
/* 效果 */
.red-color {
  background-image: linear-gradient(#ff6363 10%, #ffffff 60%, #ff7777be 10%) !important;
  -webkit-background-clip: text;
  color: transparent !important;
}
.yel-color {
  background-image: linear-gradient(#ffd79b 10%, #ffffff 60%, #ffc559 10%) !important;
  -webkit-background-clip: text;
  color: transparent !important;
}

.blue-color {
  background-image: linear-gradient(#caffff 10%, #ffffff 60%, #00c0ff 10%) !important;
  -webkit-background-clip: text;
  color: transparent !important;
}
.orange-color {
  background-image: linear-gradient(#ffe2cd 10%, #ffffff 60%, #fd852e 10%) !important;
  -webkit-background-clip: text;
  color: transparent !important;
}
.green-color {
  background-image: linear-gradient(#f0ffd7 10%, #ffffff 60%, #a9db52 10%) !important;
  -webkit-background-clip: text;
  color: transparent !important;
}
/* 表格 */
.table {
  width: 100%;
}

.table-th {
  width: 100%;
  height: 60px;
  background-color: #00396f;
  margin-bottom: 10px;
  display: flex;
  align-items: center;
}

.th {
  flex: 0.3;
  font-size: 32px;
  /* text-align: center; */
  color: #77b3f1;
  margin-left: 10px;
}

.table-tr {
  width: 100%;
  height: 288px;
  overflow-y: auto;
}

.table-tr::-webkit-scrollbar {
  /*滚动条整体样式*/
  width: 4px;
  /*高宽分别对应横竖滚动条的尺寸*/
  height: 1px;
  /* scrollbar-arrow-color: red; */
}

.table-tr::-webkit-scrollbar-thumb {
  border-radius: 4px;
  /* -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2); */
  background: #20aeff;
  height: 8px;
}

.tr {
  width: 100%;
  padding: 10px 0;
  background-color: #0f2b4d;
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.td {
  flex: 0.3;
  font-size: 32px;
  color: #fff;
  margin-left: 10px;
  /* text-align: center; */
}
.td > span:last-child {
  margin-left: 10px;
}

/* 下拉框 */

.select {
  display: inline-block;
  width: 300px;
  height: 55px;
  text-align: right;
  position: absolute;
  right: 120px;
  top: 0;
  z-index: 100;
}

.flow-icon {
  width: 25px;
  position: absolute;
  top: 20px;
  right: 14px;
}

.flow-icon > img {
  width: 25px;
}

.ul > div {
  width: 100%;
  height: 60px;
  line-height: 60px;
}

.ul {
  width: 100%;
  height: 60px;
  text-align: center;
  font-size: 32px;
  color: #fefefe;
  background-color: #132c4e;
  border: 1px solid #359cf8;
  border-radius: 40px;
  margin-top: 25px;
}
.ul > ul {
  overflow-y: auto;
  display: none;
}
.ul ul > li {
  width: 100%;
  height: 62px;
  line-height: 62px;
  background-color: #132c4ec2;
  padding-right: 20px;
  box-sizing: border-box;
}

.ul ul > li:hover {
  background-color: #359cf8;
}

.ul-active {
  display: block !important;
}

.ul > ul > li:first-of-type {
  border-radius: 40px 40px 0 0;
}

.ul ul::-webkit-scrollbar {
  /*滚动条整体样式*/
  width: 6px;
  /*高宽分别对应横竖滚动条的尺寸*/
  height: 1px;
  /* scrollbar-arrow-color: red; */
}

.ul ul::-webkit-scrollbar-thumb {
  border-radius: 6px;
  /* -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2); */
  background: #20aeff;
  height: 8px;
}
.s-flex {
  display: flex;
  flex-direction: row;
  align-items: center;
}
* {
  margin: 0;
  padding: 0;
}

ul {
  padding: 0;
  margin: 0;
  list-style: none;
}

[v-cloak] {
  display: none;
}

.qyhx-right-container {
  width: 1550px;
  height: 1904px;
  padding: 10px 55px 30px;
  box-sizing: border-box;
  /* background-color: #000; */
  overflow: hidden;
}

.xzscl-con {
  display: flex;
  justify-content: space-between;
}

.qyzfright-con-item {
  width: 33%;
  position: relative;
  // margin-right: 42px;
  margin-top: 52px;
  &:last-child {
    margin-right: 0;
  }
}

.qyzfright-pie_bg {
  position: absolute;
  top: 211px;
  left: 26px;
  animation: rotate 3s linear infinite;
}
.qyzfright-pie_bg1 {
  position: absolute;
  top: 160px;
  left: 86px;
  animation: rotate 3s linear infinite;
}

@keyframes rotate {
  0% {
    transform: rotate(0);
  }

  100% {
    transform: rotate(360deg);
  }
}

.font-36 {
  font-size: 36px;
}

/* 表格 */
.table {
  width: 960px;
  height: 500px;
  /* padding: 10px; */
  /* box-sizing: border-box; */
}

.table1 {
  width: 650px;
  height: 690px;
}

.table1 .th {
  width: 100%;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 27px;
  line-height: 60px;
  color: #dcefff;
  margin-left: 0;
  text-align: center;
  font-weight: 700;
}

.table1 .th_td {
  letter-spacing: 0px;
  text-align: center;
}

.table1 .tbody {
  width: 100%;
  height: calc(100% - 60px);
  /* overflow-y: auto; */
  overflow: hidden;
}

.table1 .tbody:hover {
  overflow-y: auto;
}

.table1 .tbody::-webkit-scrollbar {
  width: 4px;
  /*滚动条整体样式*/
  height: 4px;
  /*高宽分别对应横竖滚动条的尺寸*/
}

.table1 .tbody::-webkit-scrollbar-thumb {
  border-radius: 10px;
  background: #20aeff;
  height: 8px;
}

.table1 .tr {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 78px;
  line-height: 78px;
  font-size: 30px;
  color: #dcefff;
  /* cursor: pointer; */
  box-sizing: border-box;
  background-color: #0f2b4d;
  /* border-bottom: 1px solid #1d3c5f; */
  margin-bottom: 0;
  padding: 0 30px;
  box-sizing: border-box;
}

.table1 .tr_td {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  letter-spacing: 0px;
  text-align: center;
  box-sizing: border-box;
}

.table1 .tr_td > img {
  position: relative;
  top: 25px;
}

.table1 .tr:nth-child(2n) {
  background: #19345833;
  /* background: rgba(10, 39, 76); */
}

.table1 .tr:nth-child(2n + 1) {
  /* background: rgba(15, 49, 95); */
  background: #19345899;
}

.table1 .tr:hover {
  background-color: #3e74aa70;
}

.title-item {
  font-size: 32px;
  color: rgba(255, 255, 255, 0.7);
  display: flex;
}

.title-item > li {
  margin-right: 50px;
}

.title-item > li:last-child {
  margin-right: 0;
}

.title-item-active {
  color: #fff;
}

.title-text {
  line-height: 110px;
  font-size: 50px;
  color: #dcefff;
  letter-spacing: 2px;
  font-weight: 700;
}

.con {
  width: 100%;
  height: 680px;
  overflow: hidden;
  display: flex;
  justify-content: space-between;
  margin-bottom: 15px;
}

.con > div {
  width: 49%;
  overflow: hidden;
}

.hqzc-top {
  width: 100%;
  height: 590px;
  display: flex;
  justify-content: space-between;
  align-content: space-between;
}

#cyjj-chart {
  width: 620px;
  height: 760px;
}

.title-img {
  position: absolute;
  top: 32px;
  right: 45px;
  cursor: pointer;
}

.chart-detail {
  width: 244px;
  height: 531px;
  background: url('@/pages/qyzf/img/log_bg.png') no-repeat -82px 0px;
  background-size: 150% 100%;
  position: relative;
  color: #ffffff;
  font-size: 32px;

  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.mt_1 {
  margin-top: 20px;
}

.mb_1 {
  margin-bottom: 20px;
}

.detail-top {
  height: 72px;
  line-height: 72px;
  /* margin-top:52px; */
  /* margin-top: 208px; */
  font-weight: bolder;
  padding-left: 50px;
}

.detail-con {
  display: flex;
  justify-content: space-between;
  padding: 0 20px;
  margin-top: 20px;
  white-space: nowrap;
}

.el-pagination.is-background .btn-next,
.el-pagination.is-background .btn-prev,
.el-pagination.is-background .el-pager li {
  background: #5f7b96;
  color: #fff;
  font-size: 26px;
  font-weight: normal;
  height: 60px;
  line-height: 58px;
  box-sizing: border-box;
}

.el-pager li.active + li {
  border-left: 1px solid transparent !important;
}

.el-pagination.is-background .el-pager li:not(.disabled).active {
  background: #0166a6;
  border-radius: 3px 3px 3px 3px;
  height: 60px;
  line-height: 60px;
  box-sizing: border-box;
}

.el-pager li {
  background: #5f7b96;
  padding: 0 20px;
  border: 1px solid transparent;
  box-sizing: border-box;
}

.el-pagination .btn-next .el-icon,
.el-pagination .btn-prev .el-icon {
  font-size: 30px;
}

.el-pagination .btn-next,
.el-pagination .btn-prev {
  width: 60px;
}

.el-pagination__total {
  color: #fff;
  font-size: 32px !important;
  margin: 0;
  padding-right: 20px;
  margin-top: 15px;
}
.scgk-right {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  margin-top: 48px;
}

.scgk-right-item {
  width: 495px;
  height: 221px;
  background: url('../img/scgk-bg-new.png') no-repeat;
  background-size: 100% 100%;
  margin: 24px 10px;
  padding: 35px 0px;
  box-sizing: border-box;
  text-align: center;
}

.item_name {
  font-size: 36px;
  color: #ffffff;
  /* white-space: nowrap; */
  height: 48px;
  line-height: 48px;
}

.item_value {
  font-size: 80px;
}

.item_unit {
  font-size: 36px;
  color: #cde7ff;
}

/* 助企纾困情况样式 */
.zqyk-container {
  margin-top: 40px;
}

.zqyk-big-box {
  width: 100%;
  height: 588px;
  background: url('@/pages/qyzf/img/zqyk/zqyk_bkg_big.png') no-repeat;
  background-size: 100% 100%;
  position: relative;
  margin-bottom: 30px;
}

.zqyk-big-content {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  padding: 0 80px;
  box-sizing: border-box;
}

.zqyk-icon {
  width: 468px;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 60px;
}

.zqyk-icon img {
  width: 206px;
  height: 127px;
}

.zqyk-data-grid {
  flex: 1;
  display: flex;
  flex-wrap: wrap;
}

.zqyk-data-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  width: 430px;
  height: 252px;
  background: linear-gradient(272deg, rgba(0, 246, 255, 0) 0%, rgba(0, 229, 255, 0.16) 54%, rgba(0, 200, 255, 0) 100%);
}

.data-name {
  font-size: 40px;
  color: #ffffff;
  margin-bottom: 10px;
  white-space: nowrap;
}

.data-value-box {
  display: flex;
  align-content: baseline;
  align-items: baseline;
  margin-top: 44px;
}

.data-value {
  font-size: 72px;
  font-weight: bold;
  line-height: 1;
}

.data-unit {
  font-size: 28px;
  color: #cde7ff;
  margin-left: 8px;
}

.zqyk-small-boxes {
  display: flex;
  justify-content: space-between;
  gap: 30px;
}

.zqyk-small-box {
  flex: 1;
  height: 280px;
  background: url('@/pages/qyzf/img/zqyk/zqyk_bkg_small.png') no-repeat;
  background-size: 100% 100%;
  position: relative;
}

.small-box-content {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 40px 20px;
  box-sizing: border-box;
}

.small-data-name {
  font-size: 40px;
  color: #ffffff;
  margin-bottom: 20px;
  white-space: nowrap;
}

.small-data-value-box {
  display: flex;
  align-content: baseline;
  align-items: baseline;
  margin-top: 44px;
}

.small-data-value {
  font-size: 80px;
  font-weight: bold;
  line-height: 1;
}

.small-data-unit {
  font-size: 32px;
  margin-left: 4px;
}
</style>