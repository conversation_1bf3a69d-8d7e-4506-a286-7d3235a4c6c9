<template>
  <div class="mapDialogWrap">
    <el-dialog title="事件详情" :visible.sync="dialogTableVisible" append-to-body custom-class="mapDialog" :modal-append-to-body='false'>
      <div class="bodyWrapp" style="height: 800px">
        <div class="contentLeft">
          <div class="leftInner">
            <div class="subtitle">事件信息</div>
            <div class="lineClass">
              <div class="lineLabelClass">
                <div class="labelStyle">事件来源：</div>
              </div>
              <div class="lineContentClass">{{info.fromName}}</div>
            </div>
            <div class="lineClass">
              <div class="lineLabelClass">
                <div class="labelStyle">事件内容：</div>
              </div>
              <div class="lineContentClass">{{info.content}}</div>
            </div>
            <div class="lineClass">
              <div class="lineLabelClass">
                <div class="labelStyle">事件编号：</div>
              </div>
              <div class="lineContentClass">{{info.code}}</div>
            </div>
            <div class="lineClass">
              <div class="lineLabelClass">
                <div class="labelStyle">事件时间：</div>
              </div>
              <div class="lineContentClass">{{ info.time }}</div>
            </div>

            <div class="lineClass">
              <div class="lineLabelClass">
                <div class="labelStyle">事件地点：</div>
              </div>
              <div class="lineContentClass">{{info.address}}</div>
            </div>

            <div class="lineClass">
              <div class="lineLabelClass">
                <div class="labelStyle">事件类型：</div>
              </div>
              <div class="lineContentClass">{{info.type}}</div>
            </div>

            <div class="subtitle thirdtitle">上报人信息</div>

            <div class="lineClass">
              <div class="lineLabelClass">
                <div class="labelStyle">姓 名：</div>
              </div>
              <div class="lineContentClass">{{info.name}}</div>
            </div>

            <div class="lineClass">
              <div class="lineLabelClass">
                <div class="labelStyle">联系电话：</div>
              </div>
              <div class="lineContentClass">{{info.phone}}</div>
            </div>

          </div>
        </div>

        <div class="contentRight">
          <div class="leftInner">
            <div class="subtitle">进度追踪</div>
            <div class="timeLineWrap">
              <div class="timeLine" v-for="(item,i) in info.progress" :key="i">
                <div :class="{'circleClass':true,'activeColor':item.status == true}"></div>
                <div :class="{'lineClass':true,'activeBg':item.status == true}" v-if="i != 4"></div>
              </div>
            </div>
            <div class="tagWrap">
              <div v-for="(item,i) in info.progress" :class="{'txtClass':true,'activetxt':item.status == true}">{{item.name}}</div>
            </div>
            <div class="subtitle mg">{{info.depart}}</div>
            <div class="lineClass mg">
              <div class="lineLabelClass">
                <div class="labelStyle">结案时间：</div>
              </div>
              <div class="lineContentClass">{{info.finishTime}}</div>
            </div>
            <div class="lineClass mg">
              <div class="lineLabelClass">
                <div class="labelStyle">结案信息：</div>
              </div>
              <div class="lineContentClass">{{info.finishContent}}</div>
            </div>
            <div class="lineClass mg">
              <div class="lineLabelClass">
                <div class="labelStyle">图片信息：</div>
              </div>
              <div class="imgWrap">
                <!-- <img class="imgClass" :src="require(`@/assets/common/spIcon.png`)"> -->
<!--                <div class="imgClass"></div>-->
                <el-image :src="info.img" style="width: 379px;height: 213px">
                  <div slot="error" class="image-slot">
                    <i class="el-icon-picture-outline"></i>
                  </div>
                </el-image>
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>

</template>

<script>
export default {
  props: {
    info: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data () {
    return {
      dialogTableVisible: false,
    }
  },
  methods: {
    open () {
      this.dialogTableVisible = true
    }
  }
}
</script>

<style lang="less" scoped>
.mapDialogWrap {
  /deep/ .v-modal {
    width: 5120px !important;
    height: 2160px !important;
  }
}
</style>