<!--
 * @Description: 单个产业链右侧组件
 * @Version: 1.0
 * @Autor: wjb
 * @Date: 2025-09-19 14:08:20
 * @LastEditors: wjb
 * @LastEditTime: 2025-09-19 14:08:20
-->
<template>
  <div class="container">
    <div class="cons">
      <div class="con-items">
        <MainTile title="年度总体目标" size="large">
          <template #right>
            <!-- <OptionsSwitcher v-model="selectedYear" :options="yearOptions" @change="handleYearChange" /> -->
          </template>
        </MainTile>

        <div class="gridWrap">
          <div class="gridInner" v-for="(item, index) in ztmbData" :key="index">
            <div class="innerLeft">
              <div class="labelsClass">{{ item.name }}</div>
              <div class="numWrap">
                <div class="numberss" v-for="(item, i) in item.num" :key="i">
                  <div class="numbgss" v-if="item != ',' && item != '.' && item != '-'">
                    <count-to :start-val="0" :end-val="Number(item)" :duration="3000" />
                  </div>
                  <div class="numA" v-else>{{ item }}</div>
                </div>
                <div class="unitA">{{ item.unit }}</div>
              </div>
              <div :class="'gridInnerBg' + index"></div>
              <!-- <div class="mbzsClass">
                <div class="sjwsClss">目标值：</div>
                <div class="yyClass">{{ item.mbz }}{{ item.mbzUnit }}</div>
              </div> -->
            </div>
            <!-- <div class="innerRight">
              <div class="yuanChart" :id="`yuanId${index + 1}`"></div>
            </div> -->
          </div>
        </div>
      </div>
    </div>

    <div class="con">
      <div class="con-items">
        <MainTile title="重点运行指标" size="large">
          <template #right>
            <OptionsSwitcher v-model="selectedYearTwo" :options="yearOptions" @change="handleYearChangeTwo" />
          </template>
        </MainTile>

        <div class="tableWrapNew">
          <div class="trClassNew" v-for="(item, index) in lists" :key="index">
            <div class="item_name">{{ item.name }}</div>
            <div class="numWrapNew">
              <div class="item_value s-c-yellow-gradient s-w7">{{ item.value }}</div>
              <div class="item_unit">{{ item.unit }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getCsdnInterface1 } from '@/api/csdnIndexApi'
import countTo from 'vue-count-to'
import MainTile from '@/components/MainTitle.vue'
import OptionsSwitcher from '@/components/OptionsSwitcher.vue'

export default {
  name: 'DyCylRight',
  components: {
    countTo,
    MainTile,
    OptionsSwitcher,
  },
  data() {
    return {
      ztmbData: [
        {
          name: '规上总产值目标',
          num: '1168.8',
          unit: '亿元',
          mbz: '1000',
          mbzUnit: '亿元',
          wcd: '0',
        },
        {
          name: '规上工业增加值增速目标',
          num: '-22.4',
          unit: '%',
          mbz: '10',
          mbzUnit: '%',
          wcd: '0',
        },
        {
          name: '培育企业目标',
          num: '4',
          unit: '个',
          mbz: '6',
          mbzUnit: '个',
          wcd: '0',
        },
      ],
      cylId: null,
      lists: [
        {
          name: '产业总产值',
          value: '1168.84',
          unit: '亿元',
        },
        {
          name: '规上工业总产值增速',
          value: '32.7',
          unit: '%',
        },

        {
          name: '产业规上企业营收',
          value: '1249.9',
          unit: '亿元',
        },
        {
          name: '产业营收超亿元',
          value: '129',
          unit: '家',
        },
      ],
      tabList: ['2024', '2025'],
      tabIndex: 0,
      tabIndexTwo: 0,
      // OptionsSwitcher 需要的选项格式
      yearOptions: [
        // { label: '2024', value: '2024' },
        { label: '2025', value: '2024' },
      ],
      selectedYear: '2024',
      selectedYearTwo: '2024',
    }
  },

  mounted() {
    // 从路由参数中获取id，如果没有则默认为104
    this.cylId = this.$route.query.id ? parseInt(this.$route.query.id) : 104
    this.initData()
    // 移除 getNdztmbData() 调用，因为图表会在 updateZtmbData 中自动绘制
  },

  watch: {
    // 监听路由变化，当id参数改变时重新获取数据
    '$route.query.id'(newId) {
      if (newId) {
        this.cylId = parseInt(newId)
        this.initData()
        // 移除 getNdztmbData() 调用，因为图表会在 updateZtmbData 中自动绘制
      }
    },
  },

  methods: {
    // 初始化数据
    initData() {
      getCsdnInterface1('qyhx_ent_kv', { sstp: this.cylId })
        .then((res) => {
          console.log('qyhx_ent_kv 接口返回数据:', res.data)
          if (res.data && res.data.data && res.data.data.length > 0) {
            const kvData = res.data.data

            // 根据 kv_key 获取对应的值
            const getValueByKey = (key) => {
              const item = kvData.find((item) => item.kv_key === key)
              return item ? item.kv_value : '0'
            }

            // 更新年度总体目标数据
            this.updateZtmbData(kvData, getValueByKey, this.selectedYear)

            // 更新重点运行指标数据
            this.updateListsData(kvData, getValueByKey, this.selectedYearTwo)
          }
        })
        .catch((error) => {
          console.error('qyhx_ent_kv 接口调用失败:', error)
        })
    },

    // 更新年度总体目标数据
    updateZtmbData(kvData, getValueByKey, year = this.selectedYear) {
      console.log('updateZtmbData 被调用，年份:', year)
      console.log('kvData 数据:', kvData)

      // 获取接口数据，包括完成度
      const getDataWithCompletion = (key) => {
        const item = kvData.find((item) => item.kv_key === key)
        console.log(`查找完成度数据 key: ${key}, 找到的数据:`, item)
        if (item) {
          const completion = item.kv_value03 ? (parseFloat(item.kv_value03) * 100).toFixed(1) : '0'
          console.log(`${key} 完成度计算:`, completion)
          return {
            actual: item.kv_value || '0',
            target: item.kv_value02.replace('%', '') || '0',
            completion: completion,
          }
        }
        console.log(`${key} 未找到数据，返回默认值`)
        return { actual: '0', target: '0', completion: '0' }
      }

      // 根据年份动态构建数据
      const yearData = [
        {
          name: '规上总产值目标',
          ...getDataWithCompletion(`${year}年规上总产值`),
          unit: '亿元',
          mbzUnit: '亿元',
        },
        {
          name: '规上工业增加值增速目标',
          ...getDataWithCompletion(`${year}年制造业投资增速`),
          unit: '%',
          mbzUnit: '%',
        },
        {
          name: '培育企业目标',
          ...getDataWithCompletion(`${year}年培育链主企业`),
          unit: '个',
          mbzUnit: '个',
        },
      ]

      // 格式化数据
      this.ztmbData = yearData.map((item) => ({
        name: item.name,
        num: item.actual,
        unit: item.unit,
        mbz: item.target,
        mbzUnit: item.mbzUnit,
        wcd: item.completion,
      }))
      console.log('this.ztmbData', this.ztmbData)

      // 数据更新后重新绘制图表
      this.$nextTick(() => {
        this.ztmbData.forEach((el, index) => {
          let wcd = el.wcd == '-' ? '0' : el.wcd
          // this.pieEcharts(`yuanId${index + 1}`, wcd, ['#4FD6FF', '#0079E3'])
        })
      })
    },

    // 更新重点运行指标数据
    updateListsData(kvData, getValueByKey, year = this.selectedYearTwo) {
      console.log('updateListsData 被调用，年份:', year)
      console.log('重点运行指标 kvData:', kvData)

      // 2024年不需要年份前缀，2025年需要年份前缀
      const getKeyName = (baseName) => {
        return year === '2024' ? baseName : `${year}年${baseName}`
      }

      this.lists = [
        {
          name: '产业总产值',
          value: getValueByKey(getKeyName('规上工业总产值')),
          unit: '亿元',
        },
        {
          name: '产业总产值增速',
          value: getValueByKey(getKeyName('规上工业总产值增速')).replace('%', ''),
          unit: '%',
        },
        // {
        //   name: '制造业投资入库项目',
        //   value: getValueByKey(getKeyName('制造业投资入库项目')),
        //   unit: '个',
        // },
        // {
        //   name: '制造业投资额',
        //   value: getValueByKey(getKeyName('制造业投资额')),
        //   unit: '亿元',
        // },
        {
          name: '产业规上企业营收',
          value: getValueByKey(getKeyName('规上企业营业收入')),
          unit: '亿元',
        },
        {
          name: '产业营收超亿元',
          value: getValueByKey(getKeyName('营业收入超亿元企业')),
          unit: '家',
        },
      ]
      console.log('更新后的 lists 数据:', this.lists)
    },

    // 处理年份切换事件（第一个切换器）
    handleYearChange(value) {
      console.log('年份切换:', value)
      this.selectedYear = value
      const index = this.yearOptions.findIndex((option) => option.value === value)
      this.tabIndex = index
      this.tabClick(index)
    },

    // 处理年份切换事件（第二个切换器）
    handleYearChangeTwo(value) {
      console.log('重点运行指标年份切换:', value)
      this.selectedYearTwo = value
      const index = this.yearOptions.findIndex((option) => option.value === value)
      this.tabIndexTwo = index
      this.tabClickTwo(index)
    },

    tabClick(index) {
      this.tabIndex = index
      const year = index == 0 ? '2024' : '2025'
      console.log('tabClick 切换到年份:', year)

      // 重新获取数据
      getCsdnInterface1('qyhx_ent_kv', { sstp: this.cylId })
        .then((res) => {
          console.log('tabClick 接口返回数据:', res.data)
          if (res.data && res.data.data && res.data.data.length > 0) {
            const kvData = res.data.data
            const getValueByKey = (key) => {
              const item = kvData.find((item) => item.kv_key === key)
              console.log(`查找 key: ${key}, 找到的数据:`, item)
              return item ? item.kv_value : '0'
            }

            // 使用统一的方法更新数据（图表会在updateZtmbData中自动重绘）
            this.updateZtmbData(kvData, getValueByKey, year)
          } else {
            console.log('tabClick 接口返回数据为空')
          }
        })
        .catch((error) => {
          console.error('获取年度目标数据失败:', error)
        })
    },

    tabClickTwo(index) {
      this.tabIndexTwo = index
      const year = index == 0 ? '2024' : '2025'
      console.log('tabClickTwo 切换到年份:', year)

      // 重新获取数据
      getCsdnInterface1('qyhx_ent_kv', { sstp: this.cylId })
        .then((res) => {
          console.log('tabClickTwo 接口返回数据:', res.data)
          if (res.data && res.data.data && res.data.data.length > 0) {
            const kvData = res.data.data
            const getValueByKey = (key) => {
              const item = kvData.find((item) => item.kv_key === key)
              console.log(`重点运行指标查找 key: "${key}", 找到的数据:`, item)
              return item ? item.kv_value : '0'
            }

            // 使用统一的方法更新数据
            this.updateListsData(kvData, getValueByKey, year)
          } else {
            console.log('tabClickTwo 接口返回数据为空')
          }
        })
        .catch((error) => {
          console.error('获取运行指标数据失败:', error)
        })
    },

    getNdztmbData() {
      this.ztmbData.forEach((el, index) => {
        let wcd = el.wcd == '-' ? '0' : el.wcd
        // this.pieEcharts(`yuanId${index + 1}`, wcd, ['#4FD6FF', '#0079E3'])
      })
    },

    pieEcharts(id, data, color) {
      // 先销毁已存在的实例，避免内存泄漏和数据更新问题
      const chartDom = document.getElementById(id)
      if (chartDom) {
        this.$echarts.dispose(chartDom)
      }
      var myChart = this.$echarts.init(chartDom)
      var option = {
        title: [
          {
            text: '完成度',
            x: 'center',
            top: '25%',
            textStyle: {
              fontSize: '28',
              color: '#FFFFFF',
            },
            subtext: data + '%',
            x: 'center',
            top: '35%',
            subtextStyle: {
              fontSize: '28',
              color: '#FFFFFF',
            },
          },
        ],
        polar: {
          radius: ['80%', '95%'],
          center: ['50%', '50%'],
        },
        angleAxis: {
          max: 100,
          show: false,
        },
        radiusAxis: {
          type: 'category',
          show: true,
          axisLabel: {
            show: false,
          },
          axisLine: {
            show: false,
          },
          axisTick: {
            show: false,
          },
        },
        series: [
          {
            name: '',
            type: 'bar',
            roundCap: true,
            barWidth: 90,
            showBackground: true,
            backgroundStyle: {
              color: 'rgba(66, 66, 66, .3)',
            },
            data: [data],
            coordinateSystem: 'polar',
            itemStyle: {
              normal: {
                color: new this.$echarts.graphic.LinearGradient(0, 1, 0, 0, [
                  {
                    offset: 0,
                    color: color[1],
                  },
                  {
                    offset: 1,
                    color: color[0],
                  },
                ]),
              },
            },
          },
        ],
      }
      myChart.setOption(option)
    },
  },
}
</script>

<style scoped lang="less">
* {
  margin: 0;
  padding: 0;
}

[v-cloak] {
  display: none;
}

.newflex {
  display: flex;
  align-items: center;
}

.mr {
  margin-right: 50px;
}

.container {
  width: 1550px;
  height: 1904px;
  margin-right: 50px;
  box-sizing: border-box;
  /* background-color: #081932; */
}

.con {
  width: 100%;
  /* height: 910px; */
  height: 50%;
  /* margin-bottom: 15px; */
  overflow: hidden;
  display: flex;
  justify-content: space-between;
}

.cons {
  width: 100%;
  /* height: 1040px; */
  height: 50%;
  /* margin-bottom: 15px; */
  overflow: hidden;
  display: flex;
  justify-content: space-between;
}

.con-item {
  width: 48%;
  overflow: hidden;
}

.con-items {
  width: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.item-content {
  width: 100%;
  height: 770px;
  margin-top: 25px;
  /* background-color: #a0e3ff; */
  box-sizing: border-box;
  padding-top: 45px;
}

.title {
  width: 100%;
  text-align: center;
  font-size: 44px;
  font-style: italic;
  background: linear-gradient(0deg, #acddff 0%, #ffffff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  position: absolute;
}

/* 标题 */
.two-title {
  width: 100%;
  height: 95px;
  background-image: url('@/assets/img/dyCyl/twoTitle.png');
  /* background-image: url('/static/citybrain/qyzf/img/first-title.png'); */
  background-size: 100% 100%;
  padding-left: 100px;
  padding-right: 0px;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: space-between;
  /* margin-bottom: 20px; */
}

.title-text {
  line-height: 110px;
  font-size: 50px;
  color: #dcefff;
  letter-spacing: 2px;
  font-weight: 700;
  width: 100%;
  align-items: center;
  display: flex;
  align-items: center;
  /* background-color: bisque; */
}

.moreClass {
  font-size: 36px;
  cursor: pointer;
}

.cyzyClass {
  display: flex;
  flex-direction: column;
}

.partOne {
  display: flex;
  flex-direction: column;
  margin-top: 45px;
}

.subTitle {
  width: 100%;
  height: 90px;
  background-image: url('@/assets/img/dyCyl/ejbt.png');
  background-size: 100% 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 40px;
  color: #ffffff;
}

.partBot {
  width: 100%;
  height: 240px;
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  background-image: url('@/assets/img/dyCyl/subBg.png');
  background-size: 100% 100%;
}

.innerBox {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.boderOne {
  border-right: 2px solid;
  border-image: linear-gradient(
      180deg,
      rgba(255, 255, 255, 0) 10%,
      rgba(101, 184, 255, 1) 50%,
      rgba(255, 255, 255, 0) 90%
    )
    2 2 2 2;
}

.ltxt {
  font-size: 36px;
  color: #ffffff;
}

.numtxt {
  font-size: 80px;
  letter-spacing: 1px;
  background: linear-gradient(180deg, #ffffff 0%, #a0e3ff 100%);
  font-weight: bolder;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  margin-top: 36px;
}

.upClass {
  width: 100%;
  height: 100px;
  background-image: url('@/assets/img/dyCyl/lj.png');
  background-size: 100% 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 30px 0;
}

.txtS {
  font-size: 32px;
  color: #ffffff;
}

.countCard {
  margin: 0 34px;
}

.number {
  display: inline-block;
  font-size: 40px;
  color: #fff;
  font-weight: 400;
}

.number .numbg {
  display: inline-block;
  width: 55px;
  height: 75px;
  line-height: 75px;
  text-align: center;
  background: url('@/assets/img/dyCyl/num.png') no-repeat;
  background-size: contain;
  margin: 0 4px;
}

.numbg span {
  font-size: 64px;
  background: linear-gradient(180deg, #ffc460 0%, #fd852e 100%);
  font-weight: bolder;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.midClass {
  width: 100%;
  height: 235px;
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-column-gap: 20px;
  margin-bottom: 30px;
}

.cardS {
  width: 100%;
  height: 100%;
  background-image: url('@/assets/img/dyCyl/leftBottomBg.png');
  background-size: 100% 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  animation: fadeIn 4s infinite linear;
}

.txtTwo {
  font-size: 36px;
  color: #ffffff;
  margin-top: 45px;
}

.numTwo {
  font-size: 64px;
  letter-spacing: 1px;
  background: linear-gradient(180deg, #ffffff 0%, #a0e3ff 100%);
  font-weight: bolder;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.numberTwo {
  display: flex;
  align-items: center;
  /* font-size: 40px;
  color: #fff;
  font-weight: 400; */
}

.numbgTwo span {
  font-size: 64px;
  letter-spacing: 1px;
  background: linear-gradient(180deg, #ffffff 0%, #a0e3ff 100%);
  font-weight: bolder;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.tbzzWrap {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 36px;
  box-sizing: border-box;
}

.tbzzL {
  color: #ffffff;
  font-size: 28px;
}

.tbzzR {
  font-size: 28px;
  color: #f7ba1e;
}

.charOneClass {
  width: 100%;
  flex: 1;
  /* background-color: #A0E3FF; */
}

.upTwoClass {
  width: 100%;
  height: 230px;
  background: linear-gradient(90deg, rgba(0, 121, 227, 0) 10%, rgba(28, 148, 249, 0.3) 50%, rgba(0, 121, 227, 0) 90%);
  margin: 30px 0px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.upsClass {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.downClass {
  width: 100%;
  box-sizing: border-box;
  padding: 0 50px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 48px;
}

.twoClass {
  display: flex;
  align-items: center;
}

.tbLabel {
  font-size: 28px;
  color: #ffffff;
  margin-right: 24px;
}

.txtNum {
  font-size: 28px;
  color: #f7ba1e;
}

.chartTwoClass {
  width: 100%;
  flex: 1;
  /* background-color: #A0E3FF; */
}

.threeUpWrap {
  width: 100%;
  height: 230px;
  margin: 30px 0px;
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-column-gap: 12px;
}

.zsyzItem {
  width: 100%;
  height: 100%;
  background-image: url('@/assets/img/dyCyl/tai.png');
  background-size: 100% 100%;
  display: flex;
  align-items: center;
  flex-direction: column;
  animation: fadeIn 4s infinite linear;
}

.numThree {
  font-size: 88px;
  letter-spacing: 1px;
  background: linear-gradient(180deg, #ffffff 0%, #a0e3ff 100%);
  font-weight: bolder;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.txtThree {
  font-size: 36px;
  color: #ffffff;
}

.chartThreeClass {
  width: 100%;
  flex: 1;
  /* background-color: #A0E3FF; */
}

@keyframes fadeIn {
  0% {
    /* width: 10px;
    height: 10px; */
    opacity: 0;
    /*初始状态 透明度为0*/
  }

  50% {
    /* width: 20px;
    height: 20px; */
    opacity: 1;
    /*中间状态 透明度为1*/
  }

  100% {
    /* width: 10px;
    height: 10px; */
    opacity: 0;
    /*结尾状态 透明度为0*/
  }
}

/***************************** 新样式 *****************************/
.gridWrap {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-column-gap: 33px;
  grid-row-gap: 40px;
  /* flex: 1; */
  width: 100%;
  height: 980px;
  box-sizing: border-box;
  padding: 80px 60px 60px 60px;
  /* background-color: #acddff; */
}

.gridInner {
  width: 430px;
  height: 100%;
  /* background-color: #F7BA1E; */
  background-image: url('@/assets/img/dyCyl/frame.png');
  background-size: 100% 100%;
  display: flex;
  justify-content: space-evenly;
  align-items: center;
  box-sizing: border-box;
  padding: 98px 70px 80px;
}

.innerLeft {
  flex: 1;
  height: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-direction: column;
}

.labelsClass {
  font-size: 40px;
  color: #ffffff;
  text-align: center;
  height: 100px;
}

.alabelsClass {
  font-size: 32px;
  color: #ffffff;
}

.numsClass {
  display: flex;
  align-items: baseline;
}

.innerRight {
  width: 200px;
  flex-shrink: 0;
  height: 100%;
  /* background-color: #acddff; */
}

.numWrap {
  display: flex;
  align-items: baseline;
  /* margin-top: 30px; */
}

.numA {
  font-family: DINA, DINA;
  font-weight: bolder;
  font-size: 90px;
  letter-spacing: 1px;
  /* text-shadow: 0px 8px 20px rgba(0, 0, 0, 0.4039); */
  /* text-align: left; */
  font-style: normal;
  /* text-transform: none; */
  background: linear-gradient(180deg, #ffc460 0%, #fd852e 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.anumA {
  font-family: DINA, DINA;
  font-weight: bolder;
  font-size: 64px;
  letter-spacing: 1px;
  /* text-shadow: 0px 8px 20px rgba(0, 0, 0, 0.4039); */
  /* text-align: left; */
  font-style: normal;
  /* text-transform: none; */
  /* background: linear-gradient(180deg, #FFC460 0%, #FD852E 100%); */
  background: linear-gradient(180deg, #ffffff 0%, #a0e3ff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.numberss {
  display: inline-block;
  /* font-size: 40px;
  color: #fff;
  font-weight: 400; */
}

.numbgss span {
  /* font-size: 90px;
  background: linear-gradient(180deg, #FFC460 0%, #FD852E 100%);
  font-weight: bolder;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent; */
  font-family: DINA, DINA;
  font-weight: bolder;
  font-size: 70px;
  letter-spacing: 1px;
  /* text-shadow: 0px 8px 20px rgba(0, 0, 0, 0.4039); */
  /* text-align: left; */
  font-style: normal;
  /* text-transform: none; */
  background: linear-gradient(180deg, #ffc460 0%, #fd852e 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.anumbgss span {
  /* font-size: 90px;
  background: linear-gradient(180deg, #FFC460 0%, #FD852E 100%);
  font-weight: bolder;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent; */
  font-family: DINA, DINA;
  font-weight: bolder;
  font-size: 64px;
  letter-spacing: 1px;
  /* text-shadow: 0px 8px 20px rgba(0, 0, 0, 0.4039); */
  /* text-align: left; */
  font-style: normal;
  /* text-transform: none; */
  /* background: linear-gradient(180deg, #FFC460 0%, #FD852E 100%); */
  background: linear-gradient(180deg, #ffffff 0%, #a0e3ff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.unitA {
  font-weight: 400;
  font-size: 32px;
  color: #cde7ff;
  letter-spacing: 1px;
  margin-left: 20px;
}

.aunitA {
  font-weight: 400;
  font-size: 56px;
  color: #cde7ff;
  letter-spacing: 1px;
  margin-left: 20px;
}

.mbzsClass {
  display: flex;
  align-items: center;
}

.sjwsClss {
  font-size: 28px;
  color: #ffffff;
}

.yyClass {
  font-size: 28px;
  color: #f7ba1e;
}

.asjwsClss {
  font-size: 24px;
  color: #ffffff;
}

.ayyClass {
  font-size: 24px;
  color: #f7ba1e;
}

.yuanChart {
  width: 100%;
  height: 100%;
}

.tableWrap {
  width: 100%;
  height: 770px;
  display: flex;
  justify-content: space-between;
}

.tableWrapNew {
  width: 100%;
  /* height: 770px; */
  height: 100%;
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-template-rows: repeat(2, 1fr);
  grid-column-gap: 24px;
  grid-row-gap: 60px;
  box-sizing: border-box;
  padding: 80px 0px 40px 0;
}

.trClassNew {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  /* background-color: #FD852E; */
  background-image: url('@/assets/img/dyCyl/xz_bg.png');
  background-size: 100% 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-evenly;
  align-items: center;
}

.item_name {
  font-size: 50px;
  color: #ffffff;
  /* white-space: nowrap; */
  height: 48px;
  line-height: 48px;
}

.item_value {
  font-size: 80px;
  margin-right: 20px;
  font-weight: bold;
}

.item_unit {
  font-size: 36px;
  color: #cde7ff;
}

.numWrapNew {
  display: flex;
  /* align-items: flex-end; */
  align-items: baseline;
}

.table {
  width: 49%;
  height: 770px;
  /* padding: 10px; */
  box-sizing: border-box;
  overflow-y: auto;
  margin-top: 25px;
  /* background-color: #FD852E; */
}

.tbody {
  width: 100%;
  /* height: 100%; */
  height: calc(100% - 20px);
  /* overflow-y: auto; */
  overflow: hidden;
}

.tbody:hover {
  overflow-y: auto;
}

.tbody::-webkit-scrollbar {
  width: 4px;
  /*滚动条整体样式*/
  height: 4px;
  /*高宽分别对应横竖滚动条的尺寸*/
}

.tbody::-webkit-scrollbar-thumb {
  border-radius: 10px;
  background: #20aeff;
  height: 8px;
}

.trClass {
  width: 100%;
  height: 200px;
  background-image: url('@/assets/img/dyCyl/zdyxzbg.png');
  background-size: 100% 100%;
  margin-bottom: 36px;
  display: flex;
  align-items: center;
}

.leftPart {
  width: 50%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  border-right: 2px solid;
  border-image: linear-gradient(
      180deg,
      rgba(255, 255, 255, 0) 10%,
      rgba(101, 184, 255, 1) 50%,
      rgba(255, 255, 255, 0) 90%
    )
    2 2 2 2;
}

.rightPart {
  width: 50%;
  height: 100%;
  /* background-color: #FD852E; */
}

.hillClass {
  width: 100%;
  height: 100%;
}

.zfWrap {
  width: 100%;
  height: 100px;
  background-image: url('@/assets/img/dyCyl/luj.png');
  background-size: 100% 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 25px;
}

.chartWrap {
  flex: 1;
  width: 100%;
  /* background-color: #FFC460; */
}
</style>
<style>
.gridInnerBg0 {
  width: 240px;
  height: 205px;
  background-image: url('@/assets/img/dyCyl/gridInnerBg0.png');
  background-size: 100% 100%;
}
.gridInnerBg1 {
  width: 240px;
  height: 205px;
  background-image: url('@/assets/img/dyCyl/gridInnerBg1.png');
  background-size: 100% 100%;
}
.gridInnerBg2 {
  width: 240px;
  height: 205px;
  background-image: url('@/assets/img/dyCyl/gridInnerBg2.png');
  background-size: 100% 100%;
}
</style>
