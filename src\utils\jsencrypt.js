import JSEncrypt from 'jsencrypt/bin/jsencrypt.min'

// 密钥对生成 http://web.chacuo.net/netrsakeypair

const publicKey = 'MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBAI2Y2GSQW7v1Sl03LZo3HXvC9VIDkCpx6c7Wc7sFQks/ylFtXGBXUMJKSGbyryyzdfO0NhxORUVOwAHoWRhg7dkCAwEAAQ=='

const privateKey = 'MIIBVAIBADANBgkqhkiG9w0BAQEFAASCAT4wggE6AgEAAkEAjZjYZJBbu/VKXTctmjcde8L1UgOQKnHpztZzuwVCSz/KUW1cYFdQwkpIZvKvLLN187Q2HE5FRU7AAehZGGDt2QIDAQABAkAmeSQ61tqGDJUDFO4IG/ef3A4qFZvDXKZJtA1IAJrNtQTP5CasF+K5DdQ98moWBwjq3+C1MM0180riofWo2NxdAiEAyf4neZ10VcgXndgqE3yR42WiYvhmfo+UNsI5A907m0MCIQCzdMU86Xb83IvAmZXQ43tW3ZaYtrWj2cqu4x4gncZKswIgXaAhWfyClNRHJhGxBxFBad6NE0TB9T3F3E/QL5tW6pMCICjhE8EhabKGRuuT0PXprAIIrjKRhGUTysD2CbniVvZTAiEAx0lHW8htHPAOSa0n94l/zfTK59ec1xpZ0BL4AscofQo='

// 加密
export function encrypt(txt) {
  const encryptor = new JSEncrypt()
  encryptor.setPublicKey(publicKey) // 设置公钥
  return encryptor.encrypt(txt) // 对数据进行加密
}

// 解密
export function decrypt(txt) {
  const encryptor = new JSEncrypt()
  encryptor.setPrivateKey(privateKey) // 设置私钥
  return encryptor.decrypt(txt) // 对数据进行解密
}

