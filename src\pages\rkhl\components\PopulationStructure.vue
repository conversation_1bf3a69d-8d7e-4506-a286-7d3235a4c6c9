<template>
  <div class="chart-item">
    <SubTitle title="年龄结构" />
    <div class="chart-container">
      <div class="structure-charts">
        <div id="ageChart" class="age-chart"></div>
      </div>
    </div>
  </div>
</template>

<script>
import SubTitle from '@/components/SubTitle.vue'
import { getCsdnInterface1 } from '@/api/csdnIndexApi'

export default {
  name: 'PopulationStructure',
  components: {
    SubTitle,
  },
  mounted() {
    this.fetchPopulationStructureData()
  },
  methods: {
    // 获取人员结构数据
    async fetchPopulationStructureData() {
      try {
        // 使用与FertilityStatus相同的接口调用方式
        const titleType = '人员结构-年龄'
        const response = await getCsdnInterface1('rkhl_test', { type: titleType })

        console.log('人员结构接口返回数据:', response.data)

        // 处理接口返回的数据
        if (response.data && response.data.successful && response.data.data) {
          const processedData = this.processStructureData(response.data.data)
          this.initAgeChart(processedData.ageData)
        } else {

        }
      } catch (error) {
      }
    },

    // 处理人员结构数据
    processStructureData(data) {
      if (Array.isArray(data) && data.length > 0) {
        // 处理年龄数据，合并男女数据
        const ageData = data.map(item => ({
          name: item.name,
          value: (parseFloat(item.value) || 0) + (parseFloat(item.value1) || 0)  // 合并男女数据
        }))

        return { ageData }
      }

      // 默认数据
      return {
        ageData: [
        ]
      }
    },

    // 性别结构图表
    initGenderChart(malePercent, femalePercent) {
      const chart = this.$echarts.init(document.getElementById('genderChart'))
      const bodyMax = 100 // 指定图形界限的值

      // 导入图片资源
      const menImg = require('@/assets/img/rkhl/men.png')
      const men2Img = require('@/assets/img/rkhl/men2.png')
      const womenImg = require('@/assets/img/rkhl/women.png')
      const women2Img = require('@/assets/img/rkhl/women2.png')

      const labelSetting = {
        normal: {
          show: true,
          position: 'bottom',
          offset: [0, -150],
          formatter: function (param) {
            return ((param.value / bodyMax) * 100).toFixed(1) + '%'
          },
          textStyle: {
            fontSize: 24,
            color: '#fff',
          },
          z: 20,
        },
      }

      const option = {
        tooltip: {
          show: false, // 鼠标放上去显示悬浮数据
        },
        grid: {
          top: '10%',
          left: '5%',
          bottom: '15%',
          containLabel: true,
        },
        xAxis: {
          data: ['男性', '女性'],
          axisTick: {
            show: false,
          },
          axisLine: {
            show: false,
          },
          axisLabel: {
            show: false,
          },
        },
        yAxis: {
          max: bodyMax,
          splitLine: {
            show: false,
          },
          axisTick: {
            // 刻度线
            show: false,
          },
          axisLine: {
            // 轴线
            show: false,
          },
          axisLabel: {
            // 轴坐标文字
            show: false,
          },
        },
        series: [
          {
            name: '',
            type: 'pictorialBar',
            symbolClip: true,
            symbolBoundingData: bodyMax,
            label: labelSetting,
            data: [
              {
                value: malePercent,
                symbol: 'image://' + men2Img,
                itemStyle: {
                  normal: {
                    color: '#2bbded', // 单独控制颜色
                  },
                },
              },
              {
                value: femalePercent,
                symbol: 'image://' + women2Img,
                itemStyle: {
                  normal: {
                    color: '#eac253', // 单独控制颜色
                  },
                },
              },
            ],
            z: 20,
          },
          {
            // 设置背景底色，不同的情况用这个
            name: 'full',
            type: 'pictorialBar', // 异型柱状图 图片、SVG PathData
            symbolBoundingData: bodyMax,
            animationDuration: 0,
            itemStyle: {
              normal: {
                color: '#ccc', // 设置全部颜色，统一设置
              },
            },
            z: 10,
            data: [
              {
                itemStyle: {
                  normal: {
                    color: 'rgba(68, 195, 234, 0.80)', // 单独控制颜色
                  },
                },
                value: 100,
                symbol: 'image://' + menImg,
              },
              {
                itemStyle: {
                  normal: {
                    color: 'rgba(242, 168, 102, 0.40)', // 单独控制颜色
                  },
                },
                value: 100,
                symbol: 'image://' + womenImg,
              },
            ],
          },
        ],
      }

      chart.setOption(option)
      chart.getZr().on('mousemove', () => {
        chart.getZr().setCursorStyle('default')
      })
    },

    // 年龄结构竖向柱状图
    initAgeChart(ageData) {
      const chart = this.$echarts.init(document.getElementById('ageChart'))

      // 使用实际的年龄段数据
      const ageGroups = ageData.map(item => item.name)
      const values = ageData.map(item => item.value)

      const option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          backgroundColor: 'rgba(0, 0, 0, 0.8)',
          borderColor: '#00c0ff',
          textStyle: {
            color: '#fff',
            fontSize: 24,
          },
          formatter: function(params) {
            return `${params[0].name}<br/><span style="color:${params[0].color}">人数: ${params[0].value}万人</span>`
          }
        },
        grid: {
          left: '10%',
          right: '10%',
          top: '15%',
          bottom: '15%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: ageGroups,
          axisLabel: {
            color: '#fff',
            fontSize: 24,
            rotate: 30,
            interval: 0
          },
          axisLine: {
            lineStyle: {
              color: '#333'
            }
          },
          axisTick: {
            show: false
          }
        },
        yAxis: {
          type: 'value',
          axisLabel: {
            color: '#fff',
            fontSize: 24,
            formatter: function(value) {
              return value + '万人'
            }
          },
          axisLine: {
            lineStyle: {
              color: '#333'
            }
          },
          splitLine: {
            lineStyle: {
              color: '#333'
            }
          }
        },
        series: [
          {
            name: '人数',
            type: 'bar',
            data: values,
            itemStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 1,
                x2: 0,
                y2: 0,
                colorStops: [
                  { offset: 0, color: '#0AB7FF' },
                  { offset: 1, color: '#00E4FF' }
                ]
              }
            },
            barWidth: 30,
            label: {
              show: true,
              position: 'top',
              color: '#fff',
              fontSize: 20,
              formatter: function(params) {
                return params.value + '万人'
              }
            }
          }
        ]
      }
      chart.setOption(option)
    },
  },
}
</script>

<style lang="less" scoped>
.chart-item {
  flex: 1;
  background: transparent;
  border-radius: 8px;
  padding: 10px;
  border: none;

  .chart-container {
    position: relative;

    .structure-charts {
      width: 100%;
      height: 470px;

      .age-chart {
        width: 100%;
        height: 100%;
      }
    }
  }
}
</style>
