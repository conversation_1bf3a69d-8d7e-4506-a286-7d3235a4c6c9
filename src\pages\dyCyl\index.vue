<!--
 * @Description: 
 * @Version: 1.0
 * @Autor: wjb
 * @Date: 2025-04-14 11:38:22
 * @LastEditors: wjb
 * @LastEditTime: 2025-06-11 15:25:44
-->
<template>
  <div class="qyzf-container">
    <div class="inneLeft">
      <cylLeft
        class="animate__animated animate__fadeInLeft"
        @show-enterprise-dialog="showEnterpriseDialog"
        @openDialog="openQyhx"
      />
    </div>
    <div class="midBottom animate__animated animate__fadeIn" >
      <cylCenter />
    </div>
    <div class="innerRight">
      <cylRight class="animate__animated animate__fadeInLeft" />
    </div>

    <!-- 企业信息弹窗 -->
    <EnterpriseInfoDialog
      :visible.sync="dialogVisible"
      :dialogData="dialogData"
      @close="handleDialogClose"
    />
  </div>
</template>

<script>
import wrapbox from '@/components/wrapbox'
import cylLeft from './components/dyCylLeft.vue'
import cylCenter from './components/dyCylCenter.vue'
import cylRight from './components/dyCylRight.vue'
import EnterpriseInfoDialog from '@/components/EnterpriseInfoDialog.vue'

export default {
  name: 'index',
  data() {
    return {
      visible: false,
      qyInfoVisible: false,
      qylx: '',
      allMessage: [],
      // 企业信息弹窗相关数据
      dialogVisible: false,
      dialogData: {},
    }
  },
  components: {
    wrapbox,
    cylLeft,
    cylCenter,
    cylRight,
    EnterpriseInfoDialog,
  },
  computed: {},
  mounted() {},
  methods: {
    // 显示企业信息弹窗
    showEnterpriseDialog(dialogData) {
      this.dialogData = dialogData
      this.dialogVisible = false
    },
    // qyhx 详情弹窗（来自 openDialog 事件）
    openQyhx(value) {
      let allMessage = [{ qymc: value[0].qymc, tyshxydm: value[0].tyshxydm }]
      this.$router.push({ path: 'qyhx', query: { allMessage: JSON.stringify(allMessage) } })
    },
    // 关闭弹窗
    handleDialogClose() {
      this.dialogVisible = false
      this.dialogData = {}
    },
  },
  watch: {},
}
</script>

<style scoped lang="less">
.qyzf-container {
  position: relative;
  width: 100%;
  height: 100%;
  // z-index: 2;
  // background: url('~@/assets/application/dtBg.png') no-repeat center center;
  // background-size: 100% 100%;
  .inneLeft {
    position: absolute;
    top: 229px;
    z-index: 2;
    display: flex;
    justify-content: space-between;
  }
  .innerRight {
    position: absolute;
    top: 229px;
    right: 0;
    z-index: 2;
    display: flex;
    justify-content: space-between;
    width: 1600px;
    height: 1900px;
    z-index: 20;
    margin-top: 10px;
  }
  .midBottom {
    z-index: 2;
    position: absolute;
    left: 1920px;
    top: 220px;
    width: 1550px;
    height: 1900px;
    z-index: 20;
  }
}
</style>
