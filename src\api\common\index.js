import { request } from '@/utils/request'
import { requestLogin1 } from '@/utils/request1'

// 根据字典类型查询字典数据信息
export function getDicts(dictType) {
  return request({
    url: '/ywtg-api/system/dict/data/type/' + dictType,
    method: 'get',
  })
}

// 登陆
export function login(data) {
  return requestLogin1({
    url: '/login',
    method: 'post',
    data: data,
  })
}

// 登陆Secret
export function loginSecret(secret) {
  return requestLogin1({
    url: '/ywtg-api/dddl/ByTemporaryVoucherGetToken',
    method: 'post',
    data: {
      sign: secret,
    },
  })
}
