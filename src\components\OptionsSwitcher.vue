<template>
  <div class="options-switcher">
    <slot name="left" />

    <div
      v-for="option in options"
      :key="option.value"
      class="option-item"
      :class="{ 'option-item--active': currentValue === option.value }"
      @click="handleValueChange(option.value)"
    >
      {{ option.label }}
    </div>

    <slot name="right" />
  </div>
</template>

<script>
export default {
  name: 'OptionsSwitcher',
  props: {
    value: {
      type: [String, Number],
      default: '',
    },
    options: {
      type: Array,
      default: () => [],
      required: true,
    },
  },
  data() {
    return {
      currentValue: this.value,
    }
  },
  watch: {
    value(newVal) {
      this.currentValue = newVal
    },
  },
  methods: {
    handleValueChange(value) {
      this.currentValue = value
      this.$emit('input', value)
      this.$emit('change', value)
    },
  },
}
</script>

<style lang="less" scoped>
.options-switcher {
  display: flex;
  gap: 32px;
  align-items: center;
  position: relative;
  justify-content: flex-end;
}

.option-item {
  font-size: 32px;
  color: #9FABC1;
  transition: all 0.3s ease;
  cursor: pointer;

  &--active {
    color: #fff;
    font-weight: bolder;
    text-shadow: 0px 0px 8px #1677ff;
  }
}
</style>
