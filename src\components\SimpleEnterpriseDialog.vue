<template>
  <el-dialog
    :visible.sync="dialogVisible"
    :title="dialogTitle"
    width="80%"
    top="5vh"
    custom-class="enterprise-dialog"
    :before-close="handleClose"
    append-to-body
  >
    <div class="dialog-content">
      <!-- 标签页导航 -->
      <el-tabs v-model="activeTab" @tab-click="handleTabClick">
        <el-tab-pane label="企业列表" name="enterprise">
          <div class="enterprise-list">
            <div class="filter-bar" v-if="showFilter">
              <el-button 
                :type="gsActive === 1 ? 'primary' : 'default'"
                size="small"
                @click="toggleFilter(1)"
              >
                规上企业
              </el-button>
              <el-button 
                :type="gsActive === 2 ? 'primary' : 'default'"
                size="small"
                @click="toggleFilter(2)"
              >
                婺商企业
              </el-button>
            </div>
            
            <el-table
              v-loading="loading"
              :data="enterpriseList"
              stripe
              style="width: 100%"
              @row-click="handleRowClick"
            >
              <el-table-column prop="gsmc" label="企业名称" min-width="200" show-overflow-tooltip />
              <el-table-column prop="zcdz" label="注册地址" min-width="250" show-overflow-tooltip />
              <el-table-column prop="clrq" label="成立时间" width="120" />
              <el-table-column prop="zczb" label="注册资本" width="120">
                <template slot-scope="scope">
                  {{ scope.row.zczb || '-' }}万元
                </template>
              </el-table-column>
              <el-table-column prop="qytsbq" label="企业标签" min-width="200">
                <template slot-scope="scope">
                  <el-tag
                    v-for="tag in formatTags(scope.row.qytsbq)"
                    :key="tag"
                    size="mini"
                    style="margin-right: 5px; margin-bottom: 2px;"
                  >
                    {{ tag }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="100">
                <template slot-scope="scope">
                  <el-button type="text" size="small" @click.stop="viewDetail(scope.row)">
                    查看详情
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
            
            <div class="pagination-wrapper">
              <el-pagination
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="pagination.current"
                :page-sizes="[10, 20, 50, 100]"
                :page-size="pagination.size"
                layout="total, sizes, prev, pager, next, jumper"
                :total="pagination.total"
              />
            </div>
          </div>
        </el-tab-pane>
        
        <el-tab-pane label="专利信息" name="patent">
          <div class="patent-content">
            <div class="chart-section">
              <el-row :gutter="20">
                <el-col :span="8">
                  <div class="chart-card">
                    <h4>专利类型分布</h4>
                    <div ref="patentTypeChart" style="height: 300px;"></div>
                  </div>
                </el-col>
                <el-col :span="8">
                  <div class="chart-card">
                    <h4>专利申请趋势</h4>
                    <div ref="patentTrendChart" style="height: 300px;"></div>
                  </div>
                </el-col>
                <el-col :span="8">
                  <div class="chart-card">
                    <h4>专利发布趋势</h4>
                    <div ref="patentPublishChart" style="height: 300px;"></div>
                  </div>
                </el-col>
              </el-row>
            </div>
            
            <div class="table-section">
              <h4>专利列表</h4>
              <el-table
                v-loading="patentLoading"
                :data="patentList"
                stripe
                style="width: 100%"
              >
                <el-table-column type="index" label="序号" width="60" />
                <el-table-column prop="title" label="专利名称" min-width="200" show-overflow-tooltip />
                <el-table-column prop="pat_type" label="专利类型" width="120" />
                <el-table-column prop="issue_date" label="授权时间" width="120" />
                <el-table-column prop="applicant_name" label="专利权所属" min-width="150" show-overflow-tooltip />
              </el-table>
              
              <div class="pagination-wrapper">
                <el-pagination
                  @size-change="handlePatentSizeChange"
                  @current-change="handlePatentCurrentChange"
                  :current-page="patentPagination.current"
                  :page-sizes="[10, 20, 50]"
                  :page-size="patentPagination.size"
                  layout="total, sizes, prev, pager, next, jumper"
                  :total="patentPagination.total"
                />
              </div>
            </div>
          </div>
        </el-tab-pane>
        
        <el-tab-pane label="人才需求" name="talent">
          <div class="talent-content">
            <div class="overview-cards">
              <el-row :gutter="20">
                <el-col :span="12" v-for="(item, index) in talentOverview" :key="index">
                  <div class="overview-card">
                    <div class="card-icon">
                      <i :class="item.icon"></i>
                    </div>
                    <div class="card-content">
                      <div class="card-title">{{ item.name }}</div>
                      <div class="card-value">{{ item.value }} {{ item.unit }}</div>
                    </div>
                  </div>
                </el-col>
              </el-row>
            </div>
            
            <div class="talent-charts">
              <el-row :gutter="20">
                <el-col :span="12">
                  <div class="chart-card">
                    <h4>经验要求岗位分布</h4>
                    <div ref="experienceChart" style="height: 350px;"></div>
                  </div>
                </el-col>
                <el-col :span="12">
                  <div class="chart-card">
                    <h4>学历要求岗位分布</h4>
                    <div ref="educationChart" style="height: 350px;"></div>
                  </div>
                </el-col>
              </el-row>
            </div>
          </div>
        </el-tab-pane>
        
        <el-tab-pane label="招引清单" name="investment">
          <div class="investment-content">
            <el-table
              v-loading="investmentLoading"
              :data="investmentList"
              stripe
              style="width: 100%"
            >
              <el-table-column prop="gsmc" label="企业名称" min-width="200" show-overflow-tooltip />
              <el-table-column prop="zcdz" label="注册地址" min-width="200" show-overflow-tooltip />
              <el-table-column prop="clrq" label="成立时间" width="120" />
              <el-table-column prop="fzsz" label="评级" width="100" />
              <el-table-column label="操作" width="100">
                <template slot-scope="scope">
                  <el-button type="text" size="small" @click="viewInvestmentDetail(scope.row)">
                    查看详情
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
            
            <div class="pagination-wrapper">
              <el-pagination
                @size-change="handleInvestmentSizeChange"
                @current-change="handleInvestmentCurrentChange"
                :current-page="investmentPagination.current"
                :page-sizes="[10, 20, 50]"
                :page-size="investmentPagination.size"
                layout="total, sizes, prev, pager, next, jumper"
                :total="investmentPagination.total"
              />
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
  </el-dialog>
</template>

<script>
import { getCsdnInterface1 } from '@/api/csdnIndexApi'

export default {
  name: 'SimpleEnterpriseDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: '企业信息'
    },
    nodeData: {
      type: Object,
      default: () => ({})
    },
    showFilter: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      dialogVisible: false,
      activeTab: 'enterprise',
      loading: false,
      patentLoading: false,
      investmentLoading: false,
      gsActive: 0,
      
      // 企业列表数据
      enterpriseList: [],
      pagination: {
        current: 1,
        size: 10,
        total: 0
      },
      
      // 专利数据
      patentList: [],
      patentPagination: {
        current: 1,
        size: 10,
        total: 0
      },
      
      // 人才需求数据
      talentOverview: [
        {
          name: '需求总人数',
          value: '0',
          unit: '人',
          icon: 'el-icon-user'
        },
        {
          name: '发布职位总数',
          value: '0',
          unit: '个',
          icon: 'el-icon-document'
        }
      ],
      
      // 招引清单数据
      investmentList: [],
      investmentPagination: {
        current: 1,
        size: 10,
        total: 0
      }
    }
  },
  computed: {
    dialogTitle() {
      return this.nodeData.name || this.title
    }
  },
  watch: {
    visible: {
      handler(val) {
        this.dialogVisible = val
        if (val) {
          this.initData()
        }
      },
      immediate: true
    },
    dialogVisible(val) {
      this.$emit('update:visible', val)
    }
  },
  methods: {
    initData() {
      this.activeTab = 'enterprise'
      this.gsActive = 0
      this.loadEnterpriseData()
    },
    
    handleClose() {
      this.$emit('close')
    },
    
    handleTabClick(tab) {
      switch (tab.name) {
        case 'enterprise':
          this.loadEnterpriseData()
          break
        case 'patent':
          this.loadPatentData()
          break
        case 'talent':
          this.loadTalentData()
          break
        case 'investment':
          this.loadInvestmentData()
          break
      }
    },
    
    toggleFilter(type) {
      this.gsActive = this.gsActive === type ? 0 : type
      this.pagination.current = 1
      this.loadEnterpriseData()
    },
    
    formatTags(tags) {
      if (Array.isArray(tags) && tags.length > 0) {
        return tags
      }
      return ['暂无']
    },
    
    // 企业相关方法
    loadEnterpriseData() {
      this.loading = true
      // 模拟API调用
      setTimeout(() => {
        this.enterpriseList = [
          {
            gsmc: '示例企业1',
            zcdz: '浙江省金华市婺城区',
            clrq: '2020-01-01',
            zczb: '1000',
            tyshxydm: '123456789',
            qytsbq: ['高新技术企业', '规上企业']
          },
          {
            gsmc: '示例企业2',
            zcdz: '浙江省金华市义乌市',
            clrq: '2019-05-15',
            zczb: '500',
            tyshxydm: '987654321',
            qytsbq: ['婺商企业']
          }
        ]
        this.pagination.total = 2
        this.loading = false
      }, 1000)
    },
    
    loadPatentData() {
      this.patentLoading = true
      setTimeout(() => {
        this.patentList = []
        this.patentPagination.total = 0
        this.patentLoading = false
      }, 1000)
    },
    
    loadTalentData() {
      // 加载人才需求数据
      this.talentOverview[0].value = '150'
      this.talentOverview[1].value = '25'
    },
    
    loadInvestmentData() {
      this.investmentLoading = true

      // 调用招引清单接口
      getCsdnInterface1('qyhx_ent_zs', {
        pagenum: this.investmentPagination.current,
        pagesize: this.investmentPagination.size,
        ld_name: this.nodeData.ssjd, // 所属节点
        // 其他可能的参数...
      }).then((res) => {
        console.log('招引清单数据:', res.data)
        this.investmentLoading = false

        if (res.data && res.data.length > 0 && res.data[0].result) {
          const data = res.data[0]
          this.investmentPagination.total = data.total

          // 转换数据格式以匹配组件期望的格式
          this.investmentList = data.result.map((item) => {
            return {
              id: item.id,
              gsmc: item.qymc,           // 企业名称
              zcdz: item.zcdz,           // 注册地址
              clrq: item.clsj,           // 成立时间
              zczb: item.zczb,           // 注册资本
              fzsz: `评级：${item.pf}`,   // 评级
              tyshxydm: item.id,         // 使用id作为标识
              qytsbq: ['招引清单企业']    // 默认标签
            }
          })
        } else {
          this.investmentList = []
          this.investmentPagination.total = 0
        }
      }).catch((error) => {
        console.error('获取招引清单数据失败:', error)
        this.investmentLoading = false
        this.investmentList = []
        this.investmentPagination.total = 0
      })
    },
    
    handleRowClick(row) {
      this.$emit('row-click', row)
    },
    
    viewDetail(row) {
      this.$emit('view-detail', row)
    },
    
    viewInvestmentDetail(row) {
      this.$emit('view-investment-detail', row)
    },
    
    // 分页方法
    handleSizeChange(val) {
      this.pagination.size = val
      this.pagination.current = 1
      this.loadEnterpriseData()
    },
    
    handleCurrentChange(val) {
      this.pagination.current = val
      this.loadEnterpriseData()
    },
    
    handlePatentSizeChange(val) {
      this.patentPagination.size = val
      this.patentPagination.current = 1
      this.loadPatentData()
    },
    
    handlePatentCurrentChange(val) {
      this.patentPagination.current = val
      this.loadPatentData()
    },
    
    handleInvestmentSizeChange(val) {
      this.investmentPagination.size = val
      this.investmentPagination.current = 1
      this.loadInvestmentData()
    },
    
    handleInvestmentCurrentChange(val) {
      this.investmentPagination.current = val
      this.loadInvestmentData()
    }
  }
}
</script>

<style lang="less" scoped>
/deep/ .enterprise-dialog {
  .el-dialog__header {
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
    color: #fff;
    padding: 20px 30px;

    .el-dialog__title {
      color: #ffd700;
      font-size: 20px;
      font-weight: bold;
    }

    .el-dialog__close {
      color: #fff;
      font-size: 20px;

      &:hover {
        color: #ffd700;
      }
    }
  }

  .el-dialog__body {
    padding: 0;
    max-height: 70vh;
    overflow-y: auto;
  }
}

.dialog-content {
  padding: 20px;
}

.filter-bar {
  margin-bottom: 20px;

  .el-button {
    margin-right: 10px;
  }
}

.pagination-wrapper {
  margin-top: 20px;
  text-align: center;
}

.chart-section {
  margin-bottom: 30px;
}

.chart-card {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  height: 100%;

  h4 {
    margin: 0 0 15px 0;
    color: #1e3c72;
    font-size: 16px;
    text-align: center;
  }
}

.table-section {
  h4 {
    color: #1e3c72;
    font-size: 18px;
    margin-bottom: 15px;
    border-bottom: 2px solid #ffd700;
    padding-bottom: 8px;
  }
}

.overview-cards {
  margin-bottom: 30px;
}

.overview-card {
  display: flex;
  align-items: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  padding: 20px;
  color: #fff;
  height: 100px;

  .card-icon {
    font-size: 36px;
    margin-right: 20px;
    opacity: 0.8;
  }

  .card-content {
    flex: 1;
  }

  .card-title {
    font-size: 14px;
    opacity: 0.9;
    margin-bottom: 8px;
  }

  .card-value {
    font-size: 24px;
    font-weight: bold;
  }
}

.talent-charts {
  .chart-card {
    background: #fff;
    border: 1px solid #e6e6e6;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
}

/deep/ .el-tabs {
  .el-tabs__header {
    margin-bottom: 20px;
  }

  .el-tabs__nav-wrap {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 5px;
  }

  .el-tabs__item {
    color: #666;
    font-weight: 500;

    &.is-active {
      color: #1e3c72;
      background: #fff;
      border-radius: 6px;
    }

    &:hover {
      color: #1e3c72;
    }
  }

  .el-tabs__active-bar {
    display: none;
  }
}

/deep/ .el-table {
  .el-table__header {
    th {
      background: #f8f9fa;
      color: #333;
      font-weight: 600;
    }
  }

  .el-table__row {
    &:hover {
      background: #f0f9ff;
    }
  }
}

/deep/ .el-tag {
  background: #e6f7ff;
  border-color: #91d5ff;
  color: #1890ff;
}

/deep/ .el-button--text {
  color: #1890ff;

  &:hover {
    color: #40a9ff;
  }
}

.patent-content,
.talent-content,
.investment-content {
  min-height: 400px;
}

// 响应式设计
@media (max-width: 768px) {
  /deep/ .enterprise-dialog {
    width: 95% !important;
    margin: 0 auto;
  }

  .overview-cards {
    .el-col {
      margin-bottom: 15px;
    }
  }

  .chart-section {
    .el-col {
      margin-bottom: 20px;
    }
  }
}
</style>
