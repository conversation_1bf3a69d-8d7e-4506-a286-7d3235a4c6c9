<template>
  <div class="chart-item">
    <SubTitle title="性别结构" />
    <div class="chart-container">
      <div id="populationChart" class="chart"></div>
    </div>
  </div>
</template>

<script>
import SubTitle from '@/components/SubTitle.vue'
import { getCsdnInterface1 } from '@/api/csdnIndexApi'

export default {
  name: 'PersonnelRanking',
  components: {
    SubTitle,
  },
  data() {
    return {
      chartData: [],
      loading: false,
    }
  },
  mounted() {
    this.fetchData()
  },
  methods: {
    // 获取实时人口统计数据
    async fetchData() {
      try {
        this.loading = true
        // 使用与FertilityStatus相同的接口调用方式
        const titleType = '人员结构-性别'
        const response = await getCsdnInterface1('rkhl_test', { type: titleType })

        console.log('实时人口统计接口返回数据:', response.data)

        // 处理接口返回的数据
        if (response.data && response.data.successful && response.data.data) {
          this.chartData = this.processChartData(response.data.data)
        } else {
          // 如果接口返回空数据或失败，使用默认数据
        }
      } catch (error) {
        console.error('获取实时人口统计数据失败:', error)
        // 请求失败时使用默认数据
      } finally {
        this.loading = false
        this.initChart()
      }
    },

    // 处理图表数据
    processChartData(data) {
      if (Array.isArray(data) && data.length > 0) {
        return data.map((item) => ({
          name: item.name || '未知',
          value: parseFloat(item.value) || 0, // 将百分比字符串转换为数字
        }))
      }

      // 如果数据格式不符合预期，返回默认数据
      return []
    },

    // 初始化饼图
    initChart() {
      const chart = this.$echarts.init(document.getElementById('populationChart'))

      const option = {
        tooltip: {
          trigger: 'item',
          backgroundColor: 'rgba(0, 0, 0, 0.8)',
          borderColor: '#13C2C2',
          textStyle: { color: '#fff', fontSize: 24 },
          formatter: function (params) {
            return `${params.name}: ${params.value}%`
          },
        },
        legend: {
          // orient: 'vertical',
          top: '5%',
          // top: 'center',
          textStyle: {
            color: '#fff',
            fontSize: 24,
          },
          itemWidth: 20,
          itemHeight: 20,
        },
        grid: {
          left: '20%',
          right: '20%',
          top: '10%',
          bottom: '10%',
          containLabel: true,
        },
        series: [
          {
            type: 'pie',
            radius: ['30%', '50%'],
            center: ['50%', '50%'],
            avoidLabelOverlap: false,
            itemStyle: {
              borderRadius: 10,
              borderColor: '#0a1e3c',
              borderWidth: 2,
            },
            label: {
              show: true,
              fontSize: 20,
              color: '#fff',
              formatter: '{b}: {d}%',
            },
            emphasis: {
              label: {
                show: true,
                fontSize: 28,
                fontWeight: 'bold',
              },
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)',
              },
            },
            labelLine: {
              show: true,
              lineStyle: {
                color: '#fff',
              },
            },
            data: this.chartData.map((item, index) => ({
              name: item.name,
              value: item.value,
              itemStyle: {
                color: index === 0 ? '#13C2C2' : '#FF6B9D',
              },
            })),
            animationDuration: 1000,
            animationEasing: 'cubicOut',
          },
        ],
      }
      chart.setOption(option)

      // 响应式处理
      window.addEventListener('resize', () => {
        chart.resize()
      })
    },
  },
}
</script>

<style lang="less" scoped>
.chart-item {
  flex: 1;
  background: transparent;
  border-radius: 8px;
  padding: 10px;
  border: none;

  .chart-container {
    position: relative;

    .chart {
      width: 100%;
      height: 470px;
    }
  }
}
</style>
