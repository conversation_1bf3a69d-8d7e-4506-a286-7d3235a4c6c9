<template>
  <div class="cyl-center">
    <!-- 头部列表 -->
    <div class="list-box">
      <div class="list">
        <div class="list-itemO" v-for="(item, index) in listDataTotal" :key="index">
          <p>{{ item.name }}</p>
          <span class="value-css">
            <count-to
              :start-val="0"
              :end-val="Number(item.value)"
              :duration="3000"
              :decimals="2"
              class="s-font-50"
            ></count-to>
            {{ item.unit }}
          </span>
          <!-- <img src="@/pages/qyzfCyl/img/cyl/sq3.png" alt="" width="30" style="margin-left: 20px" />
          <span style="color: #bad3ff !important; font-size: 32px">{{ item.zs }}%</span> -->
        </div>
      </div>
      <div class="list-bg"></div>
    </div>

    <div class="map_center" id="ecartDiv" style="z-index: 9">
      <div ref="myEchart" class="map-echart"></div>
      <img
        ref="mapSvg"
        id="mapSvg"
        v-show="false"
        hidden
        src="@/pages/qyzfCyl/img/cyl/76801.jpg"
        @load="onMapImageLoad"
        crossorigin="anonymous"
      />
      <!-- 热力图控制按钮 -->
      <div class="heatmap-control">
        <button @click="toggleHeatmap" :class="['heatmap-btn', { active: showHeatmap }]">
          {{ showHeatmap ? '隐藏热力图' : '显示热力图' }}
        </button>
        <!-- <button @click="resetHeatmapData" class="heatmap-btn secondary" v-if="showHeatmap">重置数据</button> -->
      </div>
    </div>
  </div>
</template>

<script>
import { setAct } from '@/utils/util.js'
import bounds2d from '@/pages/qyzfCyl/json/anlu1.json'
import mapUrl from '@/pages/qyzfCyl/img/cyl/map_bg.png'
import { getCsdnInterface1 } from '@/api/csdnIndexApi'
import { getPeopleHeat } from '@/api/rkhl/rkhl.js'
import * as echarts from 'echarts'
export default {
  name: 'rkhlCenter',
  data() {
    return {
      mapImageLoaded: false,
      listDataTotal: [
        {
          name: '实时人口',
          value: '0',
          unit: '万人',
          zs: '-',
        },
        {
          name: '常住人口',
          value: '0',
          unit: '万人',
          zs: '-',
        },
        {
          name: '户籍人口',
          value: '0',
          unit: '万人',
          zs: '-',
        },
      ],
      listData: [
        {
          name: '规上企业数',
          value: '0',
          unit: '家',
          zs: '-',
        },
        {
          name: '规上企业产值',
          value: '0',
          unit: '亿元',
          zs: '-',
        },
        // {
        //   name: '培育指数',
        //   value: '-',
        //   unit: '',
        //   zs: '-'
        // }
      ],
      cylIndex: -1,
      cylList: [
        {
          type: '1',
          cylId: 104,
          lzqy: '新能源汽车及关键零部件',
          lzhbqy: '396',
          sxyqy: '222.98',
          sxyqyZs: '30',
          sjqx: '-',
        },
        {
          type: '2',
          cylId: 103,
          lzqy: '智能光伏及新型储能',
          lzhbqy: '308',
          sxyqy: '291.32',
          sxyqyZs: '-27.5',
          sjqx: '-',
        },
        {
          type: '3',
          cylId: 211,
          lzqy: '电动工具',
          lzhbqy: '460',
          sxyqy: '131.14',
          sxyqyZs: '17.5',
          sjqx: '-',
        },
        {
          type: '4',
          cylId: 102,
          lzqy: '纺织服装',
          lzhbqy: '1527',
          sxyqy: '244.34',
          sxyqyZs: '10',
          sjqx: '-',
        },
        {
          type: '5',
          cylId: 106,
          lzqy: '生物医药及植入性医疗器械',
          lzhbqy: '107',
          sxyqy: '55.72',
          sxyqyZs: '-5.2',
          sjqx: '-',
        },
        {
          type: '6',
          cylId: 212,
          lzqy: '磁性材料',
          lzhbqy: '121',
          sxyqy: '65.55',
          sxyqyZs: '-7.4',
          sjqx: '-',
        },
        {
          type: '7',
          cylId: 214,
          lzqy: '集成电路及信创',
          lzhbqy: '31',
          sxyqy: '20.23',
          sxyqyZs: '45.9',
          sjqx: '-',
        },
        {
          type: '8',
          cylId: 215,
          lzqy: '电子化学品',
          lzhbqy: '31',
          sxyqy: '16.3',
          sxyqyZs: '3.5',
          sjqx: '-',
        },
        {
          type: '9',
          cylId: 216,
          lzqy: '工业机床',
          lzhbqy: '154',
          sxyqy: '20.28',
          sxyqyZs: '1.8',
          sjqx: '-',
        },
        {
          type: '10',
          cylId: 217,
          lzqy: '机器人',
          lzhbqy: '44',
          sxyqy: '20.17',
          sxyqyZs: '6.9',
          sjqx: '-',
        },
      ],
      animationData: [
        // {
        //   city: '安陆市',
        //   num: 10,
        //   point: [113.632, 31.291],
        // },
        {
          city: '王义贞镇',
          num: 0,
          point: [113.379, 31.346],
        },
        {
          city: '孛畈镇',
          num: 0,
          point: [113.505, 31.382],
        },
        {
          city: '烟店镇',
          num: 0,
          point: [113.606, 31.323],
        },
        {
          city: '雷公镇',
          num: 0,
          point: [113.476, 31.296],
        },
        {
          city: '洑水镇',
          num: 0,
          point: [113.704, 31.348],
        },
        {
          city: '接官乡',
          num: 0,
          point: [113.744, 31.414],
        },
        {
          city: '赵棚镇',
          num: 0,
          point: [113.848, 31.394],
        },

        {
          city: '陈店乡',
          num: 0,
          point: [113.805, 31.289],
        },
        {
          city: '木梓乡',
          num: 0,
          point: [113.529, 31.223],
        },
        {
          city: '棠棣镇',
          num: 0,
          point: [113.615, 31.241],
        },
        {
          city: '李店镇',
          num: 0,
          point: [113.778, 31.22],
        },

        {
          city: '开发区',
          num: 0,
          point: [113.713, 31.285],
        },
        {
          city: '府城街道',
          num: 0,
          point: [113.682, 31.276],
        },
        {
          city: '南城街道',
          num: 0,
          point: [113.688, 31.202],
        },
        {
          city: '巡店镇',
          num: 0,
          point: [113.568, 31.155],
        },
        {
          city: '辛榨乡',
          num: 0,
          point: [113.633, 31.111],
        },
      ],
      // 热力图数据 - 格式为 [经度, 纬度, 数值] - 增加更多数据点实现片状效果
      heatmapData: [
      ],
      // 热力图配置
      heatmapConfig: {
        show: true,
        type: 'heatmap',
        coordinateSystem: 'geo',
        data: [],
        pointSize: 50, // 增大点大小
        blurSize: 40, // 增大模糊半径实现片状效果
        minOpacity: 0.3,
        maxOpacity: 0.9,
        gradientColors: [
          '#313695',
          '#4575b4',
          '#74add1',
          '#abd9e9',
          '#e0f3f8',
          '#ffffcc',
          '#fee090',
          '#fdae61',
          '#f46d43',
          '#d73027',
          '#a50026',
        ],
      },
      myChartMap: null,
      time: null,
      hjPerson: '0000000', //户籍人口
      pcPerson: '0000000', //普查人口
      populationTotal: '0000000', //实时人口
      wlPerson: '0000000', // 流动人员
      hjldPerson: '0000000', //户籍、流动人口
      ldPerson: '0000000', //流动人口
      initObj: {
        hjPerson: '0000000', //户籍人口
        pcPerson: '0000000', //普查人口
        populationTotal: '0000000', //实时人口
        wlPerson: '0000000', // 流动人员
        hjldPerson: '0000000', //户籍、流动人口
        ldPerson: '0000000', //流动人口
      },
      cityName: '金华市',
      cityCode: '330700',
      showHeatmap: false, // 控制热力图显示，默认显示散点图
    }
  },

  mounted() {
    // 延迟初始化，确保DOM完全渲染
    this.$nextTick(() => {
      this.getdata()
    })
  },

  methods: {
    cleanMap() {
      // ECharts 5+ 优化：检查地图底图是否可用
      const mapImage = this.$refs.mapSvg
      let areaColorConfig

      if (mapImage && mapImage.complete && mapImage.naturalWidth > 0) {
        // 图片已加载成功，使用图片作为背景
        areaColorConfig = {
          image: mapImage,
          repeat: 'no-repeat',
        }
      } else {
        // 图片未加载或加载失败，使用备用方案
        areaColorConfig = 'rgba(0, 50, 100, 0.8)'
        console.warn('地图底图未加载，使用备用颜色方案')
      }

      let newOption = JSON.parse(JSON.stringify(this.myChartMap.getOption()))
      newOption.visualMap = {
        show: false,
      }
      newOption.series = [
        {
          type: 'map',
          map: 'jh',
          zoom: 1.2,
          roam: false,
          layoutCenter: ['50%', '50%'],
          layoutSize: '90%',
          aspectScale: 1,
          selectedMode: false,
          label: {
            //初始标签样式
            show: true,
            color: '#fff',
            position: 'inside',
            distance: 0,
            fontSize: 50,
          },
          itemStyle: {
            areaColor: areaColorConfig,
            borderColor: '#70e8e9',
            borderWidth: 3,
            emphasis: {
              label: {
                show: true,
                color: '#fff',
                fontSize: 50,
              },
              areaColor: areaColorConfig,
              borderColor: '#3fdaff',
              borderWidth: 10,
            },
          },
          zlevel: 99,
          data: [],
        },
      ]
      this.myChartMap.setOption(newOption, { notMerge: true })
    },

    getdata() {
      getCsdnInterface1('qyhx_pop_top').then((res) => {
        this.listDataTotal[0].value = res.data.data[0].count
      })
      getCsdnInterface1('/qyhx_pop_ssrktj').then((res) => {
        // 判断res.data和res.data.data有效
        if (Array.isArray(res.data.data)) {
          // 遍历接口返回的数据
          res.data.data.forEach(dataItem => {
            // 查找animationData中city与接口name相同的项
            let target = this.animationData.find(animItem => animItem.city === dataItem.name)
            // 如果找到，则将count赋给num
            if (target) {
              target.num = dataItem.count
            }
          })
          echarts.registerMap('jh', bounds2d)
          this.myChartMap = echarts.init(this.$refs.myEchart)
          // 初始化完成后立即渲染热力图
          this.initEcharts()
        }
      })
      // getCsdnInterface1('/csrk_czrk_hjrk').then(res => {
      //   console.log("ghgfdh",res)
      //   this.listDataTotal[1].value = res.data.data[0].rs
      //   this.listDataTotal[2].value = res.data.data[1].rs
      // })
      this.listDataTotal[1].value = 48.56
      this.listDataTotal[2].value = 58.39
    },

    initEcharts() {
      this.$nextTick(() => {
        // ECharts 5+ 优化：检查地图底图是否可用
        const mapImage = this.$refs.mapSvg
        let areaColorConfig

        if (mapImage && mapImage.complete && mapImage.naturalWidth > 0) {
          // 图片已加载成功，使用图片作为背景
          areaColorConfig = {
            image: mapImage,
            repeat: 'no-repeat',
          }
        }
        let option = {
          tooltip: {
            show: false,
          },
          // 添加visualMap组件用于热力图
          visualMap: {
            show: this.showHeatmap,
            min: 0,
            max: 2000, // 保持最大值，让颜色映射更合理
            left: 50,
            bottom: 500,
            text: ['高', '低'],
            realtime: false,
            calculable: true,
            inRange: {
              color: this.heatmapConfig.gradientColors,
            },
            textStyle: {
              color: '#fff',
            },
          },
          geo: [
            {
              type: 'map',
              map: 'jh',
              zoom: 1.2,
              roam: false,
              layoutCenter: ['50%', '50%'],
              layoutSize: '80%',
              aspectScale: 1,
              selectedMode: false,
              // 优化的标签配置
              // label: {
              //   show: true,
              //   color: '#fff',
              //   position: 'inside',
              //   distance: 0,
              //   fontSize: 50,
              //   fontWeight: 'bold',
              //   textShadowColor: 'rgba(0, 0, 0, 0.8)',
              //   textShadowBlur: 3,
              //   textShadowOffsetX: 1,
              //   textShadowOffsetY: 1
              // },
              // 使用图片底图的样式配置
              itemStyle: {
                areaColor: areaColorConfig,
                borderColor: '#70e8e9',
                borderWidth: 3,
                // ECharts 5+ 优化的阴影效果
                shadowColor: 'rgba(112, 232, 233, 0.3)',
                shadowBlur: 8,
                shadowOffsetX: 2,
                shadowOffsetY: 2,
                emphasis: {
                  label: {
                    show: true,
                    color: '#fff',
                    fontSize: 52,
                    fontWeight: 'bold',
                    textShadowColor: 'rgba(0, 0, 0, 0.9)',
                    textShadowBlur: 5,
                  },
                  areaColor: areaColorConfig,
                  borderColor: '#3fdaff',
                  borderWidth: 10,
                  // 悬停时的阴影效果
                  shadowColor: 'rgba(63, 218, 255, 0.5)',
                  shadowBlur: 15,
                  shadowOffsetX: 3,
                  shadowOffsetY: 3,
                },
              },
              zlevel: 99,
              data: [],
            },
          ],
          series: [], // 数据
        }

        // 添加热力图
        if (this.showHeatmap) {
          console.log('添加热力图，数据点数量:', this.heatmapData.length)
          option.series.push({
            type: 'heatmap',
            coordinateSystem: 'geo',
            data: this.heatmapData,
            pointSize: this.heatmapConfig.pointSize,
            blurSize: this.heatmapConfig.blurSize,
            minOpacity: this.heatmapConfig.minOpacity,
            maxOpacity: this.heatmapConfig.maxOpacity,
            zlevel: 100,
            tooltip: {
              show: true,
              formatter: function (params) {
                return `人口数量: ${params.data[2].toLocaleString()}人`
              },
            },
          })
        }
        // 添加散点图（原有功能）- 只在非热力图模式下显示
        if (!this.showHeatmap) {
          console.log('添加散点图，数据点数量:', this.animationData.length)
          this.animationData.forEach((item) => {
            let obj = {
              type: 'scatter',
              tooltip: {
                show: false,
              },
              zlevel: 999,
              rippleEffect: {
                brushType: 'stroke',
              },
              symbolOffset: [100, 0], //让点位往右偏一点
              coordinateSystem: 'geo',
              symbol: 'image://' + mapUrl,
              symbolSize: [530, 80],
              data: [
                {
                  name: item.city,
                  value: item.point,
                  obj: item,
                },
              ],
              label: {
                position: 'right',
                show: true,
                color: '#fff',
                fontFamily: '微软雅黑',
                padding: [0, 0, 0, -520],
                align: 'left',
                width: 200,
                height: 35,
                formatter: function (params) {
                  return `{b|${params.data.obj.city}` + `    ${params.data.obj.num}人}`
                },
                rich: {
                  b: {
                    align: 'left',
                    lineHeight: 35,
                    fontSize: 35,
                    color: '#fff',
                  },
                },
              },
              itemStyle: {
                color: 'transparent',
              },
            }
            option.series.push(obj)
          })
        }

        // ECharts 5+ 优化的选项设置
        this.myChartMap.setOption(option, {
          notMerge: true, // 使用完全替换模式，确保散点图和热力图不会同时显示
          lazyUpdate: false,
          silent: false,
        })

        // 清除之前的事件监听器
        this.myChartMap.off('click')

        // 添加窗口大小变化监听器以确保响应式
        this.addResizeListener()

        // this.myChartMap.on('click', this.echartsMapClick)
      })
    },

    // echartsMapClick(e) {
    //   if (e.data) {
    //     // console.log('地图点击', e.data, this.cylList[this.cylIndex])
    //     let cyl = this.cylList[this.cylIndex] ? this.cylList[this.cylIndex].cylId : ''
    //     let qx = e.data.name
    //     this.queryQyList(cyl, qx)
    //   }
    // },

    // queryQyList(cyl, qx) {
    //   window.parent.lay.openIframe({
    //     type: 'openIframe',
    //     name: 'qyzf-qy-list',
    //     src: baseURL.url + '/static/citybrain/qyhx/commont/qyzf-qy-list.html',
    //     left: 'calc(50% - 1554px)',
    //     top: '190px',
    //     width: '3108px',
    //     height: '1900px',
    //     zIndex: '993',
    //     argument: {
    //       status: 'qyfbQyList',
    //       type: cyl,
    //       name: qx,
    //     },
    //   })
    // },

    // 地图图片加载成功
    onMapImageLoad() {
      console.log('地图底图加载成功')
      this.mapImageLoaded = true
      // 如果ECharts已经初始化，重新设置选项以应用图片
      if (this.myChartMap) {
        this.initEcharts()
      }
    },

    // ECharts 5+ 优化的响应式处理
    addResizeListener() {
      if (this.resizeObserver) {
        this.resizeObserver.disconnect()
      }

      // 使用 ResizeObserver 替代 window.resize 事件以获得更好的性能
      if (window.ResizeObserver) {
        this.resizeObserver = new ResizeObserver(() => {
          if (this.myChartMap) {
            this.myChartMap.resize()
          }
        })
        this.resizeObserver.observe(this.$refs.myEchart)
      } else {
        // 降级方案：使用防抖的 resize 事件
        this.resizeHandler = this.debounce(() => {
          if (this.myChartMap) {
            this.myChartMap.resize()
          }
        }, 300)
        window.addEventListener('resize', this.resizeHandler)
      }
    },

    // 防抖函数
    debounce(func, wait) {
      let timeout
      return function executedFunction(...args) {
        const later = () => {
          clearTimeout(timeout)
          func(...args)
        }
        clearTimeout(timeout)
        timeout = setTimeout(later, wait)
      }
    },

    // 性能优化：批量更新数据
    updateMapData(newData) {
      if (!this.myChartMap || !newData) return

      // 使用 ECharts 5+ 的增量更新功能
      const option = {
        series: [
          {
            id: 'mapSeries',
            data: newData,
          },
        ],
      }

      this.myChartMap.setOption(option, {
        notMerge: true,
        lazyUpdate: true,
        silent: true,
      })
    },

    // 切换热力图显示
    async toggleHeatmap() {
      this.showHeatmap = !this.showHeatmap
      if (this.showHeatmap) {
        getPeopleHeat('420982').then(res => {
          console.log(res)
          this.heatmapData = res.data.data.heatmap.map(item => [item.lng, item.lat, item.count])
          console.log(this.heatmapData)
          this.initEcharts()
        })
      } else {
        this.initEcharts()
      }
    },
    // 重置为原始数据
    resetHeatmapData() {
      getPeopleHeat('420982').then(res => {
          console.log(res)
          this.heatmapData = res.data.data.heatmap.map(item => [item.lng, item.lat, item.count])
          console.log(this.heatmapData)
          this.initEcharts()
        })
    },
  },

  // 组件销毁时清理资源
  beforeDestroy() {
    // 清理 ResizeObserver
    if (this.resizeObserver) {
      this.resizeObserver.disconnect()
    }

    // 清理 resize 事件监听器
    if (this.resizeHandler) {
      window.removeEventListener('resize', this.resizeHandler)
    }

    // 销毁 ECharts 实例
    if (this.myChartMap) {
      this.myChartMap.dispose()
      this.myChartMap = null
    }
  },
}
</script>

<style scoped>
.map_center {
  width: 1800px;
  height: 1900px;
  position: relative;
  background-size: 100% 100%;
  /* 确保容器有明确的尺寸 */
  min-width: 1800px;
  min-height: 1900px;
}

.map-echart {
  width: 100%;
  height: 100%;
  background-size: 100% 100%;
  /* 确保ECharts容器正确渲染 */
  position: relative;
  z-index: 1;
}

.list-box {
  width: 1530px;
  height: 150px;
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  top: 32px;
}

.list-bg {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  background-image: url('@/pages/qyzfCyl/img/qyhx/search-list.png');
  background-size: 100% 100%;
  opacity: 0.3;
  z-index: 0;
}

.list {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;

  font-size: 40px;
  color: #bad3ff;
  line-height: 46px;
  position: absolute;
  z-index: 1;
}

.list-itemO {
  flex: 0.38;
  margin: 0 20px;
  text-align: center;
  padding-top: 15px;
  box-sizing: border-box;
}

.list-item {
  flex: 0.333;
  margin: 0 20px;
  text-align: center;
  padding-top: 15px;
  box-sizing: border-box;
}

.cursor {
  cursor: pointer;
}

.value-css {
  font-size: 48px;
  font-weight: bold;
  margin-top: 25px;
  display: inline-block;
  background: linear-gradient(180deg, #ff4f1d 0%, #ffa41d 100%);
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
  -webkit-text-fill-color: transparent;
}

.icon {
  width: 40px;
  height: 33px;
  background: url('@/pages/qyzfCyl/img/cyl/down.png') no-repeat 0px 0px;
}

.cyl-list {
  position: absolute;
  left: 100px;
  top: 100px;
  z-index: 99;
}

.cyl-list-item {
  width: 300px;
  /* height:100px; */
  background: url('@/pages/qyzfCyl/img/cyl/cyl_bg.png') no-repeat;
  background-size: 100% 100%;
  padding: 30px 30px;
  box-sizing: border-box;
  margin-bottom: 40px;
  cursor: pointer;
}

.active {
  background: url('@/pages/qyzfCyl/img/cyl/cyl_active.png') no-repeat;
  background-size: 100% 100%;
}

.cyl-list-item > p {
  font-size: 32px;
  color: #fff;
  white-space: wrap;
  line-height: 50px;
}

/* 热力图控制按钮样式 */
.heatmap-control {
  position: absolute;
  top: 200px;
  right: 100px;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.heatmap-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  color: #fff;
  padding: 12px 24px;
  border-radius: 25px;
  font-size: 16px;
  font-weight: bold;
  cursor: pointer;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
  transition: all 0.3s ease;
  outline: none;
  min-width: 140px;
}

.heatmap-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.6);
}

.heatmap-btn.active {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  box-shadow: 0 4px 15px rgba(245, 87, 108, 0.4);
}

.heatmap-btn.active:hover {
  box-shadow: 0 6px 20px rgba(245, 87, 108, 0.6);
}

.heatmap-btn.secondary {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  box-shadow: 0 4px 15px rgba(79, 172, 254, 0.4);
  font-size: 14px;
  padding: 10px 20px;
}

.heatmap-btn.secondary:hover {
  box-shadow: 0 6px 20px rgba(79, 172, 254, 0.6);
}
</style>
