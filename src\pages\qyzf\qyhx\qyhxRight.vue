<template>
  <div class="qyhx-right-container">
    <MainTitle title="企业智评" size="large"></MainTitle>
    <div class="con" style="height: 600px; display: flex; align-items: flex-start">
      <!-- 智评分数 -->
      <div class="qyhxright-con-item" style="width: 30%">
        <div id="qyhx-chart01" style="width: 100%; height: 500px"></div>
        <p class="zbfs-css">{{ currentText }}</p>
      </div>
      <!-- 企业健康评级 -->
      <div class="qyhxright-con-item" style="position: relative; width: 70%; height: 100%">
        <!-- <div id="chart02" style="width: 100%; height: 600px"></div> -->
        <div class="qypj" style="height: 65%">
          <li v-for="(item, index) in qypjData" :key="index" :style="index == 2 ? 'cursor:unset' : 'cursor:pointer'">
            <img :src="item.icon" alt="" />
            <div class="qyhx-li-content">
              <p class="pj-text">{{ item.name }}</p>
              <p class="qyhx-line" style="width: 240px"></p>
              <p class="pj-value" v-if="index != 1">
                {{ index == 1 ? parseFloat(item.value).toFixed(1) + '%' : item.value }}
                <span style="font-size: 32px" v-if="index == 0">
                  <el-popover
                    placement="bottom"
                    width="450"
                    :title="item.name"
                    trigger="hover"
                    :content="`所处行业评级企业共${item.hy}家，本企业排名${item.qy}`"
                  >
                    <span slot="reference">
                      (
                      <span class="jz">{{ item.qy }}</span>
                      /{{ item.hy }})
                    </span>
                  </el-popover>
                </span>
              </p>
              <p class="pj-value" v-else>
                <img
                  src="@/pages/qyzf/img/click-1.png"
                  @click="visible = true"
                  alt=""
                  style="width: 60px; height: 50px; cursor: pointer"
                />
              </p>
            </div>
          </li>
        </div>
        <div class="qypj-sm">
          <div class="sm-zs"></div>
          <div class="sm-wz">
            该企业健康评级得分:
            <!-- 该企业所处行业平均企业健康评级为 -->
            <span>{{ qyjkfs || '-' }}分</span>
            总评为{{ hypjpjHypj }}
          </div>
        </div>
      </div>
    </div>
    <div class="qyhxCon con-bottom">
      <!-- 企业智评分数仪表盘 -->
      <div class="con-left">
        <div
          name="pie-chart"
          :id="`pie${index}`"
          :class="[index % 2 != 0 ? '' : 'blue-bg']"
          style="width: 414px; height: 200px"
          v-for="(item, index) in leftData"
          :key="index"
        ></div>
      </div>
      <div class="con-right">
        <div :class="[index % 2 == 0 ? 'blue-bg' : '', 'qyhx-ul-list']" v-for="(item, index) in rightData" :key="index">
          <img
            v-if="rightData[index].value.length > 1"
            src="@/pages/qyzf/img/to-left.png"
            class="left-a"
            @click="tab_to_prev(index)"
          />
          <el-carousel ref="tab" arrow="never" :autoplay="false" style="width: 100%">
            <el-carousel-item v-for="(x, k) in item.value" :key="k">
              <div class="qyhx-li-list" v-for="(ele, num) in x" :key="num">
                <div class="qyhx-img">
                  <img :src="ele.imgUrl" alt="" />
                </div>
                <div class="qyhx-li-content">
                  <p>{{ ele.name }}</p>
                  <p class="qyhx-line"></p>
                  <p>
                    <span :class="['value-css', ele.unit == '-' ? 'color-size' : '']">
                      <span v-if="ele.value != '-' && typeof ele.value == 'number'">{{ Math.round(ele.value) }}</span>
                      <span>{{ ele.value }}</span>
                      <span style="font-size: 22px" v-if="ele.unit != '' && ele.unit != '-'">{{ ele.unit }}</span>
                    </span>
                  </p>
                </div>
              </div>
            </el-carousel-item>
          </el-carousel>
          <img
            v-if="rightData[index].value.length > 1"
            src="@/pages/qyzf/img/to-left.png"
            class="right-a"
            @click="tab_to_next(index)"
          />
        </div>
      </div>
    </div>
    <!-- 政策推荐弹窗 -->
    <div v-if="visible">
      <qyhxReport :visible="visible" :tyshxydm="allMessage[0].tyshxydm" @close="visible = false"></qyhxReport>
    </div>
  </div>
</template>
<script>
import { getCsdnInterface1 } from '@/api/csdnIndexApi'
import qyhxReport from '@/pages/qyzf/qyhx/qyhxReportDialog'
import MainTitle from '@/components/MainTitle.vue'
export default {
  props: {
    allMessage: {
      type: Array,
      default: () => [],
    },
  },
  components: { qyhxReport, MainTitle },
  data() {
    return {
      visible: false,
      //评分仪表盘数据
      leftData: [
        {
          name: '规模指数',
          value: 0,
          strName: 'gmzs',
        },
        {
          name: '创新指数',
          value: 0,
          strName: 'cxzs',
        },
        {
          name: '成长指数',
          value: 0,
          strName: 'czzs',
        },
        {
          name: '荣誉指数',
          value: 0,
          strName: 'ryzs',
        },
        {
          name: '风险指数',
          value: 0,
          strName: 'fxzz',
        },
      ],
      //企业健康评级左侧数据
      qyjklData: [
        {
          name: '经营活力',
          value: 0,
          strName: 'jyhl',
          keyName: 'jyhlpf',
          max: 24,
        },
        {
          name: '创新驱动',
          value: 0,
          strName: 'cxqd',
          keyName: 'cxqdpf',
          max: 19,
        },
        {
          name: '结构优化',
          value: 0,
          strName: 'jgyh',
          keyName: 'jgyhpf',
          max: 19,
        },
        {
          name: '质效提升',
          value: 0,
          strName: 'zxts',
          keyName: 'zxtspf',
          max: 19,
        },
        {
          name: '风险防范',
          value: 0,
          strName: 'fxff',
          keyName: 'fxffpf',
          max: 19,
        },
      ],
      //企业基本属性数据
      rightData: [
        {
          name: 0,
          value: [
            {
              name: '注册资本',
              value: '',
              unit: '万元',
              strName: 'zczb',
            },
            {
              name: '员工数量',
              value: '',
              unit: '人',
              strName: 'ygrsxx',
            },
            {
              name: '子公司数',
              value: '',
              unit: '',
              strName: 'zgssxx',
            },
          ],
        },
        {
          name: 1,
          value: [
            {
              name: '专利数量',
              value: '',
              unit: '',
              strName: 'zlslxx',
            },
            {
              name: '研发费用',
              value: '',
              unit: '',
              strName: 'yffyxx',
            },
            {
              name: '研发人员',
              value: '',
              unit: '',
              strName: 'yfryxx',
            },
          ],
        },
        {
          name: 2,
          value: [
            {
              name: '营收增长',
              value: '',
              unit: '万元',
              strName: 'yszzxx',
            },
            {
              name: '资产负债',
              value: '',
              unit: '万元',
              strName: 'zcfzxx',
            },
            {
              name: '人均产值',
              value: '',
              unit: '万元',
              strName: 'rjczxx',
            },
          ],
        },
        {
          name: 3,
          value: [
            {
              name: '百强企业',
              value: '',
              unit: '-',
              strName: 'bqqyxx',
            },
            {
              name: '科技荣誉',
              value: '',
              unit: '-',
              strName: 'kjryxx',
            },
            {
              name: '质量奖励',
              value: '',
              unit: '',
              strName: 'zljlxx',
            },
          ],
        },
        {
          name: 4,
          value: [
            {
              name: '安全生产',
              value: '',
              unit: '',
              strName: 'aqscpf',
            },
            {
              name: '经营风险',
              value: '',
              unit: '',
              strName: 'jyfxpf',
            },
            {
              name: '信用风险',
              value: '',
              unit: '',
              strName: 'xyfxpf',
            },
          ],
        },
      ],
      //企业健康评级 右侧数据
      qyjkrData: [
        {
          name: 0,
          value: [
            [
              {
                name: '企业净利润增长率',
                value: '-',
                unit: '%',
                strName: 'qyjzczzl',
                keyName: 'qyjlrzzl',
                imgUrl: require('@/pages/qyzf/img/xq/qyjzczzl.png'),
              },
              {
                name: '企业利润总额增长率',
                value: '-',
                unit: '%',
                strName: 'qylrzezzl',
                keyName: 'qylrzezzl',
                imgUrl: require('@/pages/qyzf/img/xq/qylrzezzl.png'),
              },
            ],
            [
              {
                name: '企业营业收入增长率',
                value: '-',
                unit: '%',
                strName: 'qyyysrzzl',
                keyName: 'qyyysrzzl',
                imgUrl: require('@/pages/qyzf/img/xq/qyyysrzzl.png'),
              },
              {
                name: '企业纳税额增长率',
                value: '-',
                unit: '%',
                strName: 'qynsezzl',
                keyName: 'qynsezzl',
                imgUrl: require('@/pages/qyzf/img/xq/qynsezzl.png'),
              },
            ],
            [
              {
                name: '企业资产周转率',
                value: '-',
                unit: '%',
                strName: 'zyzczzl',
                keyName: 'zzczzl',
                imgUrl: require('@/pages/qyzf/img/xq/zyzczzl.png'),
              },
              {
                name: '企业存货周转天数',
                value: '-',
                unit: '天',
                strName: 'qychzzts',
                keyName: 'chzzts',
                imgUrl: require('@/pages/qyzf/img/xq/qychzzts.png'),
              },
            ],
            [
              {
                name: '企业应收账款周转天数',
                value: '-',
                unit: '天',
                strName: 'qyyszkzzts',
                keyName: 'yszkzzts',
                imgUrl: require('@/pages/qyzf/img/xq/qyyszkzzts.png'),
              },
            ],
          ],
        },
        {
          name: 1,
          value: [
            [
              {
                name: '高新技术企业(中小型科技型企业)',
                value: '-',
                unit: '',
                strName: 'qyyffyzb',
                keyName: 'gxjsqypf',
                imgUrl: require('@/pages/qyzf/img/xq/qyyffyzb.png'),
              },
              {
                name: '百人专利数',
                value: '-',
                unit: '个',
                strName: 'qyyffyzzl',
                keyName: 'bmryzls',
                imgUrl: require('@/pages/qyzf/img/xq/qyyffyzzl.png'),
              },
            ],
            [
              {
                name: '百人商标数',
                value: '-',
                unit: '个',
                strName: 'qysfszyfjg',
                keyName: 'bmrysbs',
                imgUrl: require('@/pages/qyzf/img/xq/qysfszyfjg.png'),
              },
            ],
          ],
        },
        {
          name: 2,
          value: [
            [
              {
                name: '企业主营业务收入利润率',
                value: '-',
                unit: '%',
                strName: 'qyzyywsrlrl',
                keyName: 'zyywsrlrl',
                imgUrl: require('@/pages/qyzf/img/xq/qyzyywsrlrl.png'),
              },
              {
                name: '是否对外提供担保',
                value: '-',
                unit: '',
                strName: 'qyjzcbzzzl',
                keyName: 'sfdb',
                imgUrl: require('@/pages/qyzf/img/xq/qyjzcbzzzl.png'),
              },
            ],
            [
              {
                name: '近期是否存在经营异常',
                value: '-',
                unit: '',
                strName: 'qybkysxlcyryzb',
                keyName: 'jyycpf',
                imgUrl: require('@/pages/qyzf/img/xq/qybkysxlcyryzb.png'),
              },
              {
                name: '企业员工增长率',
                value: '-',
                unit: '%',
                strName: 'qyzjzl',
                keyName: 'ygzzl',
                imgUrl: require('@/pages/qyzf/img/xq/qyzjzl.png'),
              },
            ],
            [
              {
                name: '主要人员为人才占比',
                value: '-',
                unit: '%',
                strName: 'qyrczyzb',
                keyName: 'rczb',
                imgUrl: require('@/pages/qyzf/img/xq/qyrczyzb.png'),
              },
              {
                name: '是否投资其他公司股权',
                value: '-',
                unit: '',
                strName: 'qyxcpcz',
                keyName: 'sfwggq',
                imgUrl: require('@/pages/qyzf/img/xq/qyxcpcz.png'),
              },
            ],
          ],
        },
        {
          name: 3,
          value: [
            [
              {
                name: '是否有新增产品',
                value: '-',
                unit: '',
                strName: 'qyqyldscl',
                keyName: 'xzcplb',
                imgUrl: require('@/pages/qyzf/img/xq/qyqyldscl.png'),
              },
              {
                name: '是否存在招聘',
                value: '-',
                unit: '',
                strName: 'qyjzcsyl',
                keyName: 'zppf',
                imgUrl: require('@/pages/qyzf/img/xq/qyjzcsyl.png'),
              },
            ],
            [
              {
                name: '是否欠缴社保',
                value: '-',
                unit: '',
                strName: 'qymjzjz',
                keyName: 'qjsb',
                imgUrl: require('@/pages/qyzf/img/xq/qymjzjz.png'),
              },
              {
                name: '是否外地购地',
                value: '-',
                unit: '',
                strName: 'qymjss',
                keyName: 'wdgd',
                imgUrl: require('@/pages/qyzf/img/xq/qymjss.png'),
              },
            ],
          ],
        },
        {
          name: 4,
          value: [
            [
              {
                name: '企业信用风险预警评级',
                value: '-',
                unit: '',
                strName: 'qyxyfxyjpj',
                keyName: 'xyfxpf',
                imgUrl: require('@/pages/qyzf/img/xq/qyxyfxyjpj.png'),
                arr: [
                  {
                    num: '1.4',
                    pj: 'B',
                  },
                  {
                    num: '2.8',
                    pj: 'A',
                  },
                ],
              },
              {
                name: '企业安全生产评级',
                value: '-',
                unit: '',
                strName: 'qyaqscpj',
                keyName: 'aqscpf',
                imgUrl: require('@/pages/qyzf/img/xq/qyaqscpj.png'),
                arr: [
                  {
                    num: '2.4',
                    pj: 'A',
                  },
                  {
                    num: '1.2',
                    pj: 'B',
                  },
                ],
              },
            ],
            [
              {
                name: '企业劳动保障评级',
                value: '-',
                unit: '',
                strName: 'qyldbzpj',
                keyName: 'ldbzcxdjpf',
                imgUrl: require('@/pages/qyzf/img/xq/qyldbzpj.png'),
                arr: [
                  {
                    num: '3.0',
                    pj: 'A',
                  },
                  {
                    num: '2.4',
                    pj: 'B',
                  },
                  {
                    num: '1.8',
                    pj: 'C',
                  },
                  {
                    num: '1.2',
                    pj: 'D',
                  },
                ],
              },
              {
                name: '企业资产负债率',
                value: '-',
                unit: '%',
                strName: 'qyzcfzl',
                keyName: 'zcfzl',
                imgUrl: require('@/pages/qyzf/img/xq/qyzcfzl.png'),
              },
            ],
            [
              {
                name: '企业环境信用评级',
                value: '-',
                unit: '',
                strName: 'qyhjxypj',
                keyName: 'qyhjxwxypjpf',
                imgUrl: require('@/pages/qyzf/img/xq/qyhjxypj.png'),
                arr: [
                  {
                    num: '3.0',
                    pj: 'A',
                  },
                  {
                    num: '2.4',
                    pj: 'B',
                  },
                  {
                    num: '2.1',
                    pj: 'C',
                  },
                  {
                    num: '1.8',
                    pj: 'D',
                  },
                  {
                    num: '1.5',
                    pj: 'E',
                  },
                  {
                    num: '1.2',
                    pj: 'F',
                  },
                ],
              },
              {
                name: '企业公共信用评级',
                value: '-',
                unit: '',
                strName: 'qyggxypj',
                keyName: 'qyggxypjpf',
                imgUrl: require('@/pages/qyzf/img/xq/qyggxypj.png'),
                arr: [
                  {
                    num: '3.0',
                    pj: 'A',
                  },
                  {
                    num: '2.4',
                    pj: 'B',
                  },
                  {
                    num: '1.8',
                    pj: 'C',
                  },
                  {
                    num: '1.5',
                    pj: 'D',
                  },
                  {
                    num: '1.2',
                    pj: 'E',
                  },
                ],
              },
            ],
          ],
        },
      ],
      //企业属性关键字
      keyName: {},
      //企业评级数据
      qypjData: [
        {
          icon: require('@/pages/qyzf/img/qyjkpj.png'),
          name: '企业健康评级',
          value: '-',
          qy: '-',
          hy: '-',
        },
        // {
        //   icon: require('@/pages/qyzf/img/qywqfxl.png'),
        //   name: '企业外迁风险率',
        //   value: '-',
        // },
        {
          icon: require('@/pages/qyzf/img/jkbgzd.png'),
          name: '健康诊断报告',
          value: '-',
        },
      ],
      qyzpfsz: 0, //企业智评分数
      qyzpfs: 0, //企业智评分数
      currentText: '健康分数',
      qyjkfs: 0, //企业健康分数
      hypjpjHypj: '',
      hyAvg: '',
    }
  },

  mounted() {
    this.getKeyName()
    this.$nextTick(() => {
      this.initFun()
    })
  },
  methods: {
    // 验证并修复数据结构
    validateAndFixData(data) {
      if (!data || !Array.isArray(data)) return data
      return data.map((item) => {
        if (item.value && Array.isArray(item.value)) {
          item.value = item.value.map((subArray) => {
            if (Array.isArray(subArray)) {
              return subArray.map((subItem) => ({
                ...subItem,
                strName: subItem.strName || 'no-data', // 提供默认值
                name: subItem.name || '未知',
                value: subItem.value || '-',
                unit: subItem.unit || '',
              }))
            }
            return subArray
          })
        }
        return item
      })
    },
    getKeyName() {
      let that = this
      getCsdnInterface1('qyhx_ent_comment_al').then((res) => {
        that.keyName = res.data.data[0].json_result
      })
    },
    //轮播下一步操作
    tab_to_next(index) {
      this.$refs.tab[index].next()
    },
    tab_to_prev(index) {
      this.$refs.tab[index].prev()
    },
    //接口初始化
    async initFun() {
      try {
        const tyshxydm = this.allMessage[0].tyshxydm

        // 并行执行多个接口调用
        const [enterpriseRes, healthDataRes, detailsRes] = await Promise.all([
          getCsdnInterface1('qyhx_enterprise_find', { tyshxydm }),
          getCsdnInterface1('qyhx_ent_health_data', { tyshxydm }),
          getCsdnInterface1('qyhx_details', { tyshxydm })
        ])

        // 处理企业查找接口数据
        const enterpriseData = enterpriseRes.data.data[0]
        if (enterpriseData) {
          //智评分数赋值
          this.qyzpfs = enterpriseData['qyzpfs']
          this.qyzpfsz = this.qyzpfs

          // 如果有行业信息，获取行业平均分
          const mlhy = enterpriseData.mlhyZwmc || ''
          if (mlhy) {
            try {
              const avgRes = await getCsdnInterface1('qyhx_avg', { mlhy_zwmc: mlhy })
              const avgData = avgRes.data.data[0]
              this.hyAvg = avgData.qyzfHypj
            } catch (error) {
              console.error('获取行业平均分失败:', error)
            }
          }
        }

        // 处理健康数据接口
        const healthData = healthDataRes.data.data[0]
        if (healthData) {
          this.qyjkrData.forEach((ele) => {
            let onceArr = ele.value.reduce((last, next) => {
              return last.concat(next)
            }, [])
            onceArr.forEach((item) => {
              if (['xyfxpf', 'aqscpf', 'ldbzcxdjpf', 'qyhjxwxypjpf', 'qyggxypjpf'].includes(item.keyName)) {
                let onceObj = item.arr.find((a) => a.num == Number(healthData[item.keyName]).toFixed(1))
                item.value = (onceObj && onceObj.pj) || '--'
              } else {
                item.value = /[\u4e00-\u9fa5]/.test(healthData[item.keyName])
                  ? healthData[item.keyName]
                  : (Math.round(healthData[item.keyName] * 100) / 100).toFixed(1) || '--'
              }
            })
          })
          this.rightData = this.validateAndFixData(this.qyjkrData)
        }

        // 处理详情接口数据
        const detailsData = detailsRes.data.data
        if (detailsData[0]) {
          this.qyjklData.forEach((ele) => {
            ele.value = Math.round((detailsData[0][ele.keyName] / ele.max) * 100 * 10) / 10 || 0
          })
          this.qypjData[0].qy = detailsData[0].rn || '-'
          this.qypjData[0].value = detailsData[0].qypj.slice(0, 1)
          this.hypjpjHypj = detailsData[0].qypj.slice(0, 1) || '-'
          this.qyzpfsz = this.qyjkfs = detailsData[0].qyzf
          this.getPageInit()
        }

      } catch (error) {
        console.error('接口调用失败:', error)
        // 可以在这里添加错误处理逻辑，比如显示错误提示
      }
    },

    //刷新调用echarts数据
    getPageInit(text) {
      let that = this
      for (let i = 0; i < that.leftData.length; i++) {
        let id = 'pie' + i
        that.pieEcharts(id, that.leftData[i], i)
      }
      that.getChart01('qyhx-chart01', this.qyzpfsz, text)
    },
    //绘制半圆仪表盘
    pieEcharts(id, data, colorNum) {
      let myEc = this.$echarts.init(document.getElementById(id))
      var colors = ['#D1545D', '#D27D40', '#E6DA52', '#54D15D']
      var colors1 = ['#54D15D', '#E6DA52', '#D27D40', '#D1545D']
      var colorArr = data.name == '风险指数' ? colors1 : colors

      function getIdx(value) {
        var p = value,
          idx = 0
        if (p > 0 && p <= 60) {
          idx = 0
        } else if (p > 60 && p <= 75) {
          idx = 1
        } else if (p > 75 && p <= 90) {
          idx = 2
        } else if (p > 90 && p <= 100) {
          idx = 3
        }
        return idx
      }

      function getColor(value) {
        return colors[getIdx(value)]
      }
      let option = {
        tooltip: {
          formatter: '{a} : {c}',
        },
        series: [
          {
            name: '智评分数',
            type: 'gauge',
            radius: '130%',
            center: ['50%', '80%'],
            detail: {
              formatter: '{value}',
              fontSize: 32,
              color: data.value > 60 ? colors[3] : data.value > 50 ? colors[1] : data.value ? colors[0] : '',
              offsetCenter: [0, '-30%'],
              fontWeight: 'bold',
            },
            data: [
              {
                value: data.value == '-' ? 0 : data.value,
                name: data.name,
              },
            ],
            min: 0,
            max: 100,
            startAngle: 180,
            endAngle: 0,
            splitNumber: 10,
            title: {
              offsetCenter: [0, 0],
              fontSize: 32,
              color: '#fff',
            },
            pointer: {
              // 仪表盘指针
              show: false,
              length: '50%',
              width: 5,
            },
            itemStyle: {
              // 仪表盘指针样式
              color: getColor(data.value),
              // color: '#1e3a53',
            },
            axisLine: {
              lineStyle: {
                width: 10,
                color: [
                  [0.5, colors[0]],
                  [0.6, colors[1]],
                  // [0.8, colors[2]],
                  [1, colors[3]],
                  // [0.6, colorArr[0]],
                  // [0.75, colorArr[1]],
                  // [0.9, colorArr[2]],
                  // [1, colorArr[3]],
                ],
              },
            },
            axisTick: {
              show: true,
              length: 5,
              lineStyle: {
                width: 1,
              },
            },
            splitLine: {
              show: true,
              length: 10,
              lineStyle: {
                color: '#ffffff80',
              },
            },
            axisLabel: {
              textStyle: {
                fontSize: 18,
                color: '#ffffff80',
                fontWeight: '',
              },
            },
          },
        ],
      }
      myEc.clear()
      myEc.setOption(option, true)
    },
    //绘制整圆仪表盘
    getChart01(id, value, text) {
      let data = Number(value)
      let myEc = echarts.init(document.getElementById(id))
      var colors = ['#D1545D', '#D27D40', '#E6DA52', '#54D15D']
      let max = text == '企业智评' ? 120 : 100

      function getIdx(value) {
        var p = value,
          idx = 0
        if (p > 0 && p <= 40) {
          idx = 0
        } else if (p > 40 && p <= 50) {
          idx = 1
        }
        // else if (p > 75 && p <= 90) {
        //   idx = 2
        // }
        else if (p > 50 && p <= max) {
          idx = 3
        }
        return idx
      }

      function getColor(value) {
        return colors[getIdx(value)]
      }
      let option = {
        tooltip: {
          formatter: '{a} : {c}',
        },
        series: [
          {
            name: '智评分数',
            type: 'gauge',
            radius: '85%',
            center: ['47%', '70%'],
            detail: {
              formatter: '{value}',
              fontSize: 50,
              color: "#fff",
              offsetCenter: [0, '40%'],
              fontWeight: 'bold',
            },
            data: [
              {
                value: value,
                name: text,
              },
            ],
            min: 0,
            max: max,
            // startAngle: 0,
            // endAngle: 359.9,
            splitNumber: max == 120 ? 8 : 10,
            title: {
              offsetCenter: [0, '50%'],
              fontSize: 32,
              color: '#fff',
              show: true,
            },
            pointer: {
              // 仪表盘指针
              show: true,
              length: '50%',
              width: 10,
            },
            itemStyle: {
              // 仪表盘指针样式
              color: getColor(data),
              // color: getColor(data.value),
              //color: '#1e3a53',
            },
            axisLine: {
              lineStyle: {
                width: 30,
                color:
                  text == '企业智评'
                    ? [
                        [0.5, colors[0]],
                        [0.625, colors[1]],
                        [0.75, colors[2]],
                        [1, colors[3]],
                      ]
                    : [
                        // [0.5, colors[0]],
                        // [0.6, colors[1]],
                        // // [0.8, colors[2]],
                        // [1, colors[3]],
                        [0.4, colors[0]],
                        [0.5, colors[1]],
                        [1, colors[3]],
                      ],
              },
            },
            axisTick: {
              show: true,
              length: 18,
              lineStyle: {
                width: 1,
              },
            },
            splitLine: {
              show: true,
              length: 35,
              lineStyle: {
                color: '#fff',
                fontWeight: '400',
              },
            },
            axisLabel: {
              formatter: function (e) {
                if (max == 120) {
                  switch (e + '') {
                    case '30':
                      return '问题企业'
                    case '75':
                      return '合格'
                    case '90':
                      return '良好'
                    case '105':
                      return '优秀'
                    default:
                      return e
                  }
                } else {
                  // switch (e + '') {
                  //   case '40':
                  //     return '问题\n企业'
                  //   case '60':
                  //     return '亚健康'
                  //   case '70':
                  //     return '健康'
                  //   case '80':
                  //     return '优秀'
                  //   case '100':
                  //     return '标杆'
                  //   default:
                  return e
                  // }
                }
              },
              textStyle: {
                fontSize: 26,
                // color: "#fff",
                fontWeight: '400',
              },
            },
            markLine: {
              lineStyle: {
                color: '#fff',
              },
              data: [
                [
                  {
                    name: max == 120 ? '行业平均值(63)' : `行业平均值\n (${this.hyAvg})`,
                    x: '47%',
                    y: '70%',
                    xAxis: 51,
                    lineStyle: {
                      width: 1.656,
                      color: (this.hyAvg && '#fff') || 'transparent',
                    },
                    label: {
                      show: this.hyAvg != '',
                      color: '#fff',
                      fontSize: 32,
                    },
                  },
                  {
                    x: this.hyAvg * 1 + '%', //max == 120 ?  '80%': '60%',
                    y: '15.26065975587649%', //this.hyAvg*0.5+'%'//'15.26065975587649%',
                  },
                ],
              ],
            },
          },
        ],
      }
      myEc.setOption(option)
    },
  },
}
</script>

<style lang="less" scoped>
.qyhx-right-container {
  width: 1550px;
  margin-top: 229px;
  padding: 10px 55px 30px;
}
/deep/.el-carousel__container {
  height: 200px !important;
}

ul {
  padding: 0;
  margin: 0;
  list-style: none;
}
.qyhxCon {
  width: 100%;
  height: 910px;
  margin-bottom: 30px;
  overflow: hidden;
  display: flex;
  justify-content: space-between;
}

.qyhxright-con-item {
  width: 48%;
}

.qypj {
  width: 100%;
  height: 100%;
  padding: 80px 48px 80px 10px;
  box-sizing: border-box;
  display: flex;
  justify-content: space-between;
}

.qypj li {
  height: 100%;
  position: relative;
  display: flex;
  align-items: center;
  cursor: pointer;
}

/* .qypj li:last-child {
  cursor: auto;
} */
.qypj li img {
  width: 198px;
  height: 231px;
}

.pj-value {
  white-space: nowrap;
  // text-align: center;
  display: flex;
  justify-content: center;
  align-items: baseline;
  font-size: 40px;
  line-height: 62px;
  font-weight: bolder;
  width: 100%;
  margin-top: 20px;
  text-align: center;
  background: linear-gradient(180deg, #d6e7ff 0%, #9bc3ff 100%);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

.jz {
  background: linear-gradient(180deg, #d6e7ff 0%, #fea043 100%);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

.qh-con {
  width: 100%;
  height: 2px;
  background-image: url('@/pages/qyzf/img/tab-bg.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  position: relative;
}

.qyhxTab {
  width: 278px;
  height: 30px;
  background-image: url('@/pages/qyzf/img/tab.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  position: absolute;
  top: -27px;
  left: 90px;
}

.qypj-sm {
  width: 987px;
  height: 21px;
  background-image: url('@/pages/qyzf/img/zpfs-bg.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  position: relative;
  top: 120px;
  left: 280px;
}

.sm-zs {
  width: 1000px;
  height: 42px;
  background-image: url('@/pages/qyzf/img/t-zs.png');
  background-repeat: no-repeat;
  position: absolute;
  top: -56px;
  left: -42px;
}

.sm-wz {
  white-space: nowrap;
  line-height: 40px;
  color: #dcefff;
  text-align: center;
  font-size: 32px;
  position: absolute;
  top: -42px;
  left: 111px;
}

.sm-wz span {
  font-size: 36px;
  background: linear-gradient(180deg, #d6e7ff 0%, #9bc3ff 100%);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  font-weight: bolder;
  margin: 0 10px;
}

.pj-text {
  font-size: 32px;
  color: #dcefff;
  text-align: center;
  margin-top: 20px;
}

::-webkit-scrollbar {
  /*滚动条整体样式*/
  width: 6px;
  /*高宽分别对应横竖滚动条的尺寸*/
  height: 1px;
  /* scrollbar-arrow-color: red; */
}

::-webkit-scrollbar-thumb {
  border-radius: 6px;
  /* -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2); */
  background: #20aeff;
  height: 8px;
}

/* 标题 */
.first-title {
  position: relative;
  width: 100%;
  height: 95px;
  background-image: url('@/pages/qyzf/img/first-title.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  padding-left: 100px;
  padding-right: 50px;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: 20px 0;
  line-height: 110px;
  font-size: 45px;
  color: #dcefff;
  letter-spacing: 2px;
  font-weight: 700;
}

.first-title > i,
.two-title > i {
  position: absolute;
  width: 1062px;
  height: 90px;
  display: inline-block;
  background: url('@/pages/qyzf/img/line.png');
  background-size: 100%;
  animation-name: animateLine;
  animation-duration: 10s;
  animation-iteration-count: infinite;
  /* animation-direction:alternate; */
  /* animation-delay */
}

.two-title {
  position: relative;
  width: 100%;
  height: 95px;
  background-image: url('@/pages/qyzf/img/two-title.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  padding-left: 100px;
  padding-right: 50px;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: 20px 0;
  line-height: 110px;
  font-size: 45px;
  color: #dcefff;
  letter-spacing: 2px;
  font-weight: 700;
}

.title-text {
  line-height: 110px;
  font-size: 50px;
  color: #dcefff;
  letter-spacing: 2px;
  font-weight: 700;
  position: relative;
}

.flag-click::after {
  content: '';
  width: 26px;
  height: 23px;
  /* background-image: url('/static/images/common/header/click-1.png'); */
  position: absolute;
  top: 50px;
  margin-left: 10px;
}

.title-item {
  font-size: 32px;
  color: rgba(255, 255, 255, 0.843);
  display: flex;
}

.title-item > li {
  margin-right: 50px;
}

.title-item > li:last-child {
  margin-right: 0;
}

.title-item-active {
  color: #fff;
}

/* 智评分数 */
.zbfs-css {
  height: 80px;
  font-size: 32px;
  line-height: 40px;
  color: #fff;
  text-align: center;
  margin: 0 auto;
  background-image: url('@/pages/qyzf/img/zpfs-bg.png');
  background-size: 250px 35px;
  background-repeat: no-repeat;
  background-position: center 17px;
}

/* 底部 */
.con-bottom {
  height: 1080px;
  display: flex;
  justify-content: space-between;
}

.con-left {
  width: 414px;
  height: 100%;
  margin-left: 100px;
}

.con-left > div {
  margin-bottom: 15px;
}

.con-right {
  width: 1500px;
  height: 100%;
  /* background-color: teal; */
}

.blue-bg {
  background: #0ab7ff10;
}

.qyhx-ul-list {
  width: 100%;
  height: 200px;
  margin-bottom: 20px;
  font-size: 32px;
  color: #dcefff;
  display: flex;
  justify-content: space-between;
  padding-left: 80px;
  padding-right: 80px;
  box-sizing: border-box;
  position: relative;
}

.qyhx-ul-list:last-child {
  margin-bottom: 0;
}

.qyhx-li-list {
  width: 50%;
  height: 100%;
  display: flex;
  align-items: center;
}

.qyhx-img {
  width: 158px;
  height: 160px;
}

.qyhx-img > img {
  width: 100%;
  height: 100%;
}

.qyhx-li-content {
  flex: 1;
  padding-left: 0px;
  box-sizing: border-box;
  text-align: center;
}

.qyhx-line {
  width: 240px;
  height: 26px;
  border-color: #dcefff;
  background-image: url('@/pages/qyzf/img/line2.png');
  background-size: 100% 100%;
  margin-bottom: 10px;
}

.color-size {
  font-size: 36px !important;
}

.value-css {
  font-size: 42px;
  background: linear-gradient(180deg, #d6e7ff 0%, #9bc3ff 100%);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

.icon-box {
  display: flex;
  justify-content: center;
  align-items: center;
}

.icon-css {
  width: 120px;
  height: 120px;
}

.icon-css > img {
  width: 100%;
  height: 100%;
}

.el-carousel__item {
  display: flex;
}

.el-carousel__arrow {
  font-size: 40px;
  color: #3bbff8;
}

.el-icon-arrow-left:before {
  content: '\e792';
}

.el-icon-arrow-right:before {
  content: '\e791';
}

.el-carousel__indicator--horizontal {
  display: none;
}

.el-carousel__arrow--left {
  display: none;
}

.right-a {
  position: absolute;
  right: -30px;
  z-index: 9;
  cursor: pointer;
  width: 145px;
  height: 145px;
  top: 30px;
}

.left-a {
  transform: rotate(175deg);
  position: absolute;
  left: -30px;
  z-index: 9;
  cursor: pointer;
  width: 145px;
  height: 145px;
  top: 20px;
}

.color-yel {
  background: linear-gradient(180deg, #ff4f1d 0%, #ffa41d 100%);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

.el-message__content,
.el-message__icon {
  font-size: 28px;
}

.el-message--error {
  background-color: #062643;
  border: none;
}

.zctj {
  width: 220px;
  height: 60px;
  line-height: 60px;
  text-align: center;
  cursor: pointer;
  font-size: 30px;
  color: #fff;
  background-color: #3291f8;
  border-radius: 5%;
  margin-top: 16px;
}
</style>
<style lang="less">
.el-carousel__indicators {
  display: none !important;
}
.el-popover {
  background: #062845;
  color: #fff !important;
  font-size: 22px !important;
  border: none !important;
  font-size: 30px;
  .el-popover__title {
    font-size: 30px !important;
    color: #fff;
  }
}
.el-popper[x-placement^='bottom'] .popper__arrow {
  border-bottom-color: #1b5ad7;
}

.el-popper[x-placement^='bottom'] .popper__arrow::after {
  border-bottom-color: #132c4ed0;
}
</style>