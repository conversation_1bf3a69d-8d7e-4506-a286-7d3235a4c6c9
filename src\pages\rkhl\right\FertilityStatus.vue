<template>
  <div class="chart-item">
    <SubTitle title="新增人口情况" />
    <div class="chart-container">
      <div id="fertilityStatus" class="chart"></div>
    </div>
  </div>
</template>

<script>
import SubTitle from '@/components/SubTitle.vue'
import { getCsdnInterface1 } from '@/api/csdnIndexApi'

export default {
  name: 'FertilityStatus',
  components: { SubTitle },
  data() {
    return {
      chartData: {
        years: [],
        maleData: [],
        femaleData: []
      },
      loading: false,
    }
  },
  mounted() {
    this.fetchData()
  },
  methods: {
    async fetchData() {
      try {
        this.loading = true
        // 获取 SubTitle 的 title 属性作为 type 参数
        const titleType = '生育情况'
        const response = await getCsdnInterface1('rkhl_test', { type: titleType })

        console.log('接口返回数据:', response.data)

        // 处理接口返回的数据
        if (response.data && response.data.successful && response.data.data) {
          this.chartData = this.processChartData(response.data.data)
        } else {
          // 如果接口返回空数据或失败，使用默认数据
          this.chartData = {
            years: ['2021', '2022', '2023', '2024'],
            maleData: [1861, 1344, 1243, 1239],
            femaleData: [1611, 1187, 1125, 1085]
          }
        }
      } catch (error) {
        console.error('获取数据失败:', error)
        // 请求失败时使用默认数据
        this.chartData = {
          years: ['2021', '2022', '2023', '2024'],
          maleData: [1861, 1344, 1243, 1239],
          femaleData: [1611, 1187, 1125, 1085]
        }
      } finally {
        this.loading = false
        this.initChart()
      }
    },

    processChartData(data) {
      // 处理接口返回的生育情况数据
      if (Array.isArray(data) && data.length > 0) {
        const years = []
        const maleData = []
        const femaleData = []

        data.forEach(item => {
          years.push(item.name) // 年份
          maleData.push(parseInt(item.value)) // 男性数量
          femaleData.push(parseInt(item.value1)) // 女性数量
        })

        return {
          years,
          maleData,
          femaleData
        }
      }

      // 如果数据格式不符合预期，返回默认数据
      return {
        years: ['2021', '2022', '2023', '2024'],
        maleData: [1861, 1344, 1243, 1239],
        femaleData: [1611, 1187, 1125, 1085]
      }
    },

    initChart() {
      const chart = this.$echarts.init(document.getElementById('fertilityStatus'))

      const option = {
        tooltip: {
          trigger: 'axis',
          backgroundColor: 'rgba(0, 0, 0, 0.8)',
          borderColor: '#13C2C2',
          textStyle: { color: '#fff', fontSize: 24 },
          formatter: function(params) {
            let result = params[0].name + '<br/>'
            params.forEach(param => {
              result += param.marker + param.seriesName + ': ' + param.value + '人<br/>'
            })
            return result
          }
        },
        legend: {
          data: ['男性', '女性'],
          top: '5%',
          icon: 'circle',
          itemWidth: 12,
          itemHeight: 12,
          itemGap: 30,
          textStyle: {
            color: '#fff',
            fontSize: 24,
            fontFamily: 'SourceHanSansCN-Medium',
          },
        },
        grid: {
          left: '5%',
          right: '5%',
          top: '20%',
          bottom: '5%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: this.chartData.years,
          axisLabel: {
            color: '#fff',
            fontSize: 24,
            interval: 0 // 显示所有年份标签
          },
          axisLine: { lineStyle: { color: 'rgb(119,179,241,.4)' } },
          axisTick: { show: false }
        },
        yAxis: {
          type: 'value',
          name: '单位：(人)',
          nameTextStyle: {
            color: '#fff',
            fontSize: 20,
            padding: [0, 0, 10, 0],
          },
          axisLabel: { color: '#fff', fontSize: 24 },
          axisLine: { lineStyle: { color: 'rgb(119,179,241,.4)' } },
          splitLine: { lineStyle: { color: 'rgb(119,179,241,.4)' } },
        },
        series: [
          {
            name: '男性',
            type: 'bar',
            data: this.chartData.maleData,
            itemStyle: {
              color: '#00C0FF',
              borderRadius: [4, 4, 0, 0],
            },
            barWidth: 20,
            animationDuration: 1000,
            animationEasing: 'cubicOut',
          },
          {
            name: '女性',
            type: 'bar',
            data: this.chartData.femaleData,
            itemStyle: {
              color: '#FFC460',
              borderRadius: [4, 4, 0, 0],
            },
            barWidth: 20,
            animationDuration: 1000,
            animationEasing: 'cubicOut',
          },
        ],
      }
      chart.setOption(option)

      // 响应式处理
      window.addEventListener('resize', () => {
        chart.resize()
      })
    },
  },
}
</script>

<style lang="less" scoped>
.chart-item {
  flex: 1;
  background: transparent;
  border-radius: 8px;
  padding: 10px;
  border: none;

  .chart-container {
    position: relative;

    .chart {
      width: 100%;
      height: 470px;
    }
  }
}
</style>
