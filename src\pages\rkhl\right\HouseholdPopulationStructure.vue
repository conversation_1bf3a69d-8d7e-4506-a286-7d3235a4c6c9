<template>
  <div class="chart-item">
    <SubTitle title="户籍人口趋势" />
    <div class="chart-container">
      <div id="householdPopulationStructure" class="chart"></div>
    </div>
  </div>
</template>

<script>
import SubTitle from '@/components/SubTitle.vue'
import { getCsdnInterface1 } from '@/api/csdnIndexApi'
import * as echarts from 'echarts'

export default {
  name: 'HouseholdPopulationStructure',
  components: { SubTitle },
  data() {
    return {
      chartData: [],
      loading: false,
    }
  },
  mounted() {
    this.fetchData()
  },
  methods: {
    async fetchData() {
      try {
        this.loading = true
        // 获取 SubTitle 的 title 属性作为 type 参数
        const titleType = '户籍人口结构'
        const response = await getCsdnInterface1('rkhl_test', { type: titleType })

        console.log('接口返回数据:', response.data)

        // 处理接口返回的数据
        if (response.data && response.data.successful && response.data.data) {
          this.chartData = this.processChartData(response.data.data)
        } else {
          // 如果接口返回空数据或失败，使用空数组
          this.chartData = []
        }
      } catch (error) {
        console.error('获取数据失败:', error)
        // 请求失败时使用空数组
        this.chartData = []
      } finally {
        this.loading = false
        this.initChart()
      }
    },

    processChartData(data) {
      // 处理户籍人口结构数据
      if (Array.isArray(data) && data.length > 0) {
        return data.map((item) => ({
          name: item.name, // 年份
          value: parseFloat(item.value), // 人数（万人）
          rate: parseFloat(item.value1.replace('%', '')), // 变化率（去掉%符号）
        }))
      }
      return []
    },

    initChart() {
      const chart = this.$echarts.init(document.getElementById('householdPopulationStructure'))

      const categories = this.chartData.map((item) => item.name)
      const values = this.chartData.map((item) => item.value)
      const rates = this.chartData.map((item) => item.rate)

      // 计算两个y轴的范围,确保0刻度对齐
      const valueMax = Math.max(...values, 0)
      const valueMin = Math.min(...values, 0)
      const rateMax = Math.max(...rates, 0)
      const rateMin = Math.min(...rates, 0)

      // 计算比例,使0刻度对齐
      const valueRange = Math.max(Math.abs(valueMax), Math.abs(valueMin))
      const rateRange = Math.max(Math.abs(rateMax), Math.abs(rateMin))

      const option = {
        tooltip: {
          trigger: 'axis',
          backgroundColor: 'rgba(0, 0, 0, 0.8)',
          borderColor: '#0091FF',
          textStyle: { color: '#fff', fontSize: 26 },
          formatter: function (params) {
            let result = params[0].name + '<br/>'
            params.forEach((param) => {
              if (param.seriesName === '户籍人口') {
                result += param.marker + param.seriesName + ': ' + param.value + '万人<br/>'
              } else if (param.seriesName === '户籍人口增长率') {
                result += param.marker + param.seriesName + ': ' + param.value + '%<br/>'
              }
            })
            return result
          },
        },
        legend: {
          data: ['户籍人口', '户籍人口增长率'],
          textStyle: { color: '#fff', fontSize: 24 },
          top: '5%',
        },
        grid: { left: '15%', right: '14%', top: '20%', bottom: '12%' },
        xAxis: {
          type: 'category',
          data: categories,
          axisLabel: { color: '#fff', fontSize: 24 },
          axisLine: { lineStyle: { color: '#333' } },
        },
        yAxis: [
          {
            type: 'value',
            name: '单位（万人）',
            nameLocation: 'end',
            nameGap: 10,
            nameTextStyle: {
              color: '#fff',
              fontSize: 24,
            },
            min: -valueRange,
            max: valueRange,
            show: true,
            axisLabel: {
              color: '#fff',
              fontSize: 24,
            },
            axisLine: {
              show: true,
              lineStyle: { color: '#333' }
            },
            splitLine: {
              show: true,
              lineStyle: {
                type: 'dashed',
                color: 'rgba(255, 255, 255, 0.15)',
                width: 1,
              }
            },
          },
          {
            type: 'value',
            name: '%',
            nameLocation: 'end',
            nameGap: 10,
            nameTextStyle: {
              color: '#fff',
              fontSize: 24,
            },
            min: -rateRange,
            max: rateRange,
            axisLabel: {
              show: true,
              color: '#fff',
              fontSize: 24,
            },
            axisLine: {
              show: true,
              lineStyle: { color: '#333' }
            },
            splitLine: { show: false },
          },
        ],
        series: [
          {
            name: '户籍人口',
            type: 'bar',
            yAxisIndex: 0,
            data: values,
            itemStyle: {
              color: '#0091FF',
              borderRadius: [4, 4, 0, 0],
            },
            barWidth: 24,
            animationDuration: 1000,
            animationEasing: 'cubicOut',
          },
          {
            name: '户籍人口增长率',
            type: 'line',
            yAxisIndex: 1,
            data: rates,
            lineStyle: {
              color: '#FF6B35',
              width: 3,
            },
            itemStyle: {
              color: '#FF6B35',
            },
            symbol: 'circle',
            symbolSize: 8,
            animationDuration: 1500,
            animationEasing: 'cubicOut',
          },
        ],
      }
      chart.setOption(option)

      // 响应式处理
      window.addEventListener('resize', () => {
        chart.resize()
      })
    },
  },
}
</script>

<style lang="less" scoped>
.chart-item {
  flex: 1;
  background: transparent;
  border-radius: 8px;
  padding: 10px;
  border: none;

  .chart-container {
    position: relative;

    .chart {
      width: 100%;
      height: 450px;
    }
  }
}
</style>

