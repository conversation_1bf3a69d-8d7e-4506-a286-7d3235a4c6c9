import { csdnRequest, request } from '@/utils/request'

// 城市大脑指标接口
export function getCsdnInterface(url, params) {
  return csdnRequest({
    url: '/api/?indexid=/' + url,
    method: 'get',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
      portToken: sessionStorage.getItem('csdnIndexApiToken'),
      ptid: 'PT0001',
    },
    params,
  })
}

// 安陆指标
export function getCsdnInterface1(url, params) {
  return request({
    url: '/indexPort/?indexid=/' + url,
    method: 'get',
    params,
  })
}

// 安陆pdf
export function gePdf(data) {
  return request({
    url: '/enterprisePdf/getData',
    method: 'post',
    data,
  })
}

// 查询大屏-人口活力报告列表
export function geReportList() {
  return request({
    url: '/anluRkReport/anluRkReport/list',
    method: 'get',
  })
}

// 新增大屏-人口活力报告
export function addReport(data) {
  return request({
    url: '/anluRkReport/anluRkReport/add',
    method: 'post',
    data,
  })
}

export function getCsdnInterface2(url, params) {
  return csdnRequest(
    {
      url: '/data/' + url,
      method: 'get',
      headers: {
        'Content-Type': 'application/json;charset=UTF-8',
        portToken: sessionStorage.getItem('csdnIndexApiToken'),
        ptid: 'PT0001',
      },
      params,
    },
    'get'
  )
}

//大脑登录接口
export function csdnLogin(data) {
  return csdnRequest({
    url: '/adm-api/auth/login',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
  })
}

//淹没分析
export function csdnMapYmfx(data) {
  return csdnRequest({
    url: '/typeq/api/getu/project/get',
    method: 'post',
    data,
    headers: {
      'Access-Token': window.accessToken,
    },
  })
}

//鉴权接口
export function csdnAuth(data) {
  return csdnRequest({
    url: '/typeq/api/auth/creditAuth',
    method: 'post',
    data,
  })
}

//区域创建
export function csdnAreaCreate(data) {
  return csdnRequest({
    url: '/typeq/api/getu/project/create',
    method: 'post',
    data,
    headers: {
      'Access-Token': window.accessToken,
    },
  })
}

//点位搜索
export function solrSearch(data) {
  return csdnRequest({
    url: '/api2.0/solr-provider/api/data-sources/solr-search',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json',
      'Access-Token': window.accessToken,
    },
  })
}

//点位搜索
export function layerPoiSearch(data) {
  return csdnRequest({
    url: '/api2.0/solr-provider/api/data-sources/solr-search',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json',
    },
  })
}

//舆情详情
export function getYqDetail(params) {
  return csdnRequest({
    url: '/jhyjzh-server/screen_api/zhddzx/zhddzxLeft005-1',
    method: 'get',
    params,
  })
}
