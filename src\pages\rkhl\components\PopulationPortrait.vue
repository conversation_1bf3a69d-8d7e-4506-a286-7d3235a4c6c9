<template>
  <div class="chart-item">
    <SubTitle title="人口画像" />
    <div class="chart-container">
      <!-- Tab切换 -->
      <div class="tab_list">
        <div 
          :class="[tabIndex === 0 ? 'tab_item_atv_left' : '', 'tab_item']" 
          @click="tabClick(0)"
        >性别比例</div>
        <div 
          :class="[tabIndex === 1 ? 'tab_item_atv_center' : '', 'tab_item']" 
          @click="tabClick(1)"
        >年龄分布</div>
        <div 
          :class="[tabIndex === 2 ? 'tab_item_atv_right' : '', 'tab_item']" 
          @click="tabClick(2)"
        >消费水平</div>
      </div>

      <!-- 内容区域 -->
      <div class="portrait-content">
        <!-- 性别比例 -->
        <div :class="[tabIndex === 0 ? 'xbli_box' : 'd_none']">
          <div class="xbbl_left">
            <div id="manEcharts" class="gender-chart"></div>
          </div>
          <div class="xbbl_center">
            <span>总人口性别比</span>
            <p>
              <span class="s-c-blue-gradient" v-if="person === Infinity">-</span>
              <span class="s-c-blue-gradient" v-else>{{ person }}</span>
              <span>:100</span>
            </p>
          </div>
          <div class="xbbl_right">
            <div id="womanEcharts" class="gender-chart"></div>
          </div>
        </div>

        <!-- 年龄分布 -->
        <div :class="[tabIndex === 1 ? 'nlfb_box' : 'd_none']">
          <div id="nlfbPie" class="age-chart"></div>
        </div>

        <!-- 消费水平 -->
        <div :class="[tabIndex === 2 ? 'xfsp_box' : 'd_none']">
          <div id="xfsp" class="consume-chart"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import SubTitle from '@/components/SubTitle.vue'
import { getCsdnInterface } from '@/api/csdnIndexApi'

export default {
  name: 'PopulationPortrait',
  components: {
    SubTitle,
  },
  props: {
    currentLevel: {
      type: String,
      default: '重点场所',
    },
  },
  data() {
    return {
      tabIndex: 0,
      person: '',
      startName: '默认区域',
      code: null,
      xfspData: [
        { pm: '低', name: '5000元以下', value: 34.5 },
        { pm: '中', name: '5000-20000元', value: 47.9 },
        { pm: '高', name: '20000元以上', value: 15.7 },
      ],
    }
  },
  mounted() {
    this.startName = this.currentLevel
    this.tabClick(0)
  },
  watch: {
    currentLevel: {
      handler(newLevel) {
        this.handleLevelChange(newLevel)
      },
      immediate: true,
    },
  },
  methods: {
    // 处理级别变化
    handleLevelChange(level) {
      this.currentLevel = level
      this.startName = level
      // 重新初始化数据
      this.initEch(this.code || 'default')
    },

    // Tab切换
    tabClick(index) {
      this.tabIndex = index
      // 根据选择的tab初始化对应的图表
      setTimeout(() => {
        if (index === 0) {
          this.initGenderCharts()
        } else if (index === 1) {
          this.initAgeChart()
        } else if (index === 2) {
          this.initConsumeChart()
        }
      }, 100)
    },

    // 初始化人口画像数据
    async initEch(type) {
      try {
        const res = await getCsdnInterface('cstz_rjxf', { number: type })
        const data = res.data[0]
        
        const girl = Math.round(data.gender.filter((ele) => ele.subject === '女性')[0].value * 100).toFixed(2)
        const boy = Math.round(data.gender.filter((ele) => ele.subject === '男性')[0].value * 100).toFixed(2)
        
        this.person = (
          (data.gender.filter((ele) => ele.subject === '男性')[0].value /
            data.gender.filter((ele) => ele.subject === '女性')[0].value) * 100
        ).toFixed(0)
        
        // 年龄数据
        const ageArr = data.age.map((ele) => ({
          name: ele.subject,
          value: Math.round(ele.value * 100).toFixed(2),
        }))
        
        // 消费数据
        this.xfspData[0].value = Math.round(data.consume[0].value * 100).toFixed(2)
        this.xfspData[1].value = Math.round(data.consume[1].value * 100).toFixed(2)
        this.xfspData[2].value = Math.round(data.consume[2].value * 100).toFixed(2)
        
        // 渲染图表
        this.pieEchartsFun('manEcharts', '男性人口', '#00C0FF', boy)
        this.pieEchartsFun('womanEcharts', '女性人口', '#B76FD8', girl)
        this.pieEchartsFun2('nlfbPie', ageArr)
        this.showLineEcharts(this.xfspData, 'xfsp')
      } catch (error) {
        console.error('获取人口画像数据失败:', error)
        this.initMockData()
      }
    },

    // 初始化模拟数据
    initMockData() {
      this.person = '105'
      this.pieEchartsFun('manEcharts', '男性人口', '#00C0FF', '52.5')
      this.pieEchartsFun('womanEcharts', '女性人口', '#B76FD8', '47.5')
      
      const mockAgeData = [
        { name: '0-18岁', value: '25.5' },
        { name: '19-35岁', value: '35.2' },
        { name: '36-50岁', value: '28.8' },
        { name: '51岁以上', value: '10.5' },
      ]
      this.pieEchartsFun2('nlfbPie', mockAgeData)
      this.showLineEcharts(this.xfspData, 'xfsp')
    },

    // 便捷方法
    initGenderCharts() {
      this.pieEchartsFun('manEcharts', '男性人口', '#00C0FF', '52.5')
      this.pieEchartsFun('womanEcharts', '女性人口', '#B76FD8', '47.5')
    },

    initAgeChart() {
      const mockAgeData = [
        { name: '0-18岁', value: '25.5' },
        { name: '19-35岁', value: '35.2' },
        { name: '36-50岁', value: '28.8' },
        { name: '51岁以上', value: '10.5' },
      ]
      this.pieEchartsFun2('nlfbPie', mockAgeData)
    },

    initConsumeChart() {
      this.showLineEcharts(this.xfspData, 'xfsp')
    },

    // 性别比例饼图
    pieEchartsFun(dom, title, color, data) {
      const chartDom = document.getElementById(dom)
      if (!chartDom) return

      this.$echarts.dispose(chartDom)
      const chart = this.$echarts.init(chartDom)

      const option = {
        title: [
          {
            text: title,
            x: 'center',
            top: '70%',
            textStyle: {
              color: '#FFFFFF',
              fontSize: 20,
              fontWeight: '100',
            },
          },
          {
            text: data + '%',
            x: 'center',
            top: '35%',
            textStyle: {
              fontSize: '24',
              color: color,
              fontFamily: 'DINAlternate-Bold, DINAlternate',
              fontWeight: '600',
            },
          },
        ],
        polar: {
          radius: ['78%', '92%'],
          center: ['50%', '40%'],
        },
        angleAxis: {
          max: 100,
          show: false,
        },
        radiusAxis: {
          type: 'category',
          show: true,
          axisLabel: { show: false },
          axisLine: { show: false },
          axisTick: { show: false },
        },
        series: [
          {
            name: '',
            type: 'bar',
            roundCap: true,
            barWidth: 90,
            showBackground: true,
            backgroundStyle: {
              color: color + '25',
            },
            data: [data],
            coordinateSystem: 'polar',
            itemStyle: {
              normal: {
                color: color,
                shadowColor: color,
                shadowBlur: 10,
              },
            },
          },
          {
            name: '',
            type: 'pie',
            startAngle: 80,
            radius: ['70%'],
            hoverAnimation: false,
            center: ['50%', '40%'],
            itemStyle: {
              color: 'rgba(66, 66, 66, .1)',
              borderWidth: 1,
              borderColor: color + 'cc',
            },
            data: [100],
          },
        ],
      }

      chart.setOption(option)
    },

    // 年龄分布饼图
    pieEchartsFun2(dom, data) {
      const chartDom = document.getElementById(dom)
      if (!chartDom) return

      this.$echarts.dispose(chartDom)
      const chart = this.$echarts.init(chartDom)

      const option = {
        color: ['#EAEA26', '#906BF9', '#FE5656', '#01E17E', '#3DD1F9', '#FFAD05', '#FFCC99', '#4772EB'],
        tooltip: {
          trigger: 'item',
          backgroundColor: '#384c63',
          textStyle: {
            color: '#ffffff',
            fontSize: 20,
          },
          formatter: function (params) {
            return params.name + ' :  ' + params.percent + '%'
          },
        },
        legend: {
          orient: 'vertical',
          top: 'center',
          right: '10%',
          icon: 'circle',
          textStyle: {
            color: '#fff',
            fontSize: 20,
          },
          formatter: function (name) {
            const arr = data.filter((ele) => ele.name === name)
            return arr[0].name + '   ' + arr[0].value + '%'
          },
        },
        series: [
          {
            type: 'pie',
            radius: ['40%', '70%'],
            center: ['40%', '50%'],
            data: data,
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)',
              },
            },
          },
        ],
      }

      chart.setOption(option)
    },

    // 消费水平图表
    showLineEcharts(data, dom) {
      const chartDom = document.getElementById(dom)
      if (!chartDom) return

      this.$echarts.dispose(chartDom)
      const chart = this.$echarts.init(chartDom)

      const option = {
        tooltip: {
          trigger: 'item',
          backgroundColor: '#384c63',
          textStyle: {
            color: '#ffffff',
            fontSize: 20,
          },
        },
        legend: {
          top: '5%',
          textStyle: {
            color: '#fff',
            fontSize: 18,
          },
        },
        series: [
          {
            type: 'pie',
            radius: ['40%', '70%'],
            center: ['50%', '60%'],
            data: data.map(item => ({
              name: item.name,
              value: item.value
            })),
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)',
              },
            },
          },
        ],
      }

      chart.setOption(option)
    },
  },
}
</script>

<style lang="less" scoped>
.chart-item {
  flex: 1;
  background: transparent;
  border-radius: 8px;
  padding: 10px;
  border: none;

  .chart-container {
    position: relative;

    .tab_list {
      display: flex;
      justify-content: center;
      margin-bottom: 20px;

      .tab_item {
        padding: 10px 20px;
        background: rgba(0, 74, 166, 0.3);
        color: #bfbcbc;
        cursor: pointer;
        border: 2px solid #215293;
        font-size: 16px;
        transition: all 0.3s ease;

        &:first-child {
          border-radius: 8px 0 0 8px;
        }

        &:last-child {
          border-radius: 0 8px 8px 0;
        }

        &.tab_item_atv_left,
        &.tab_item_atv_center,
        &.tab_item_atv_right {
          background: rgba(0, 74, 166, 0.8);
          color: #ffffff;
          font-weight: bold;
        }

        &:hover {
          background: rgba(0, 74, 166, 0.6);
          color: #ffffff;
        }
      }
    }

    .portrait-content {
      .d_none {
        display: none;
      }

      .xbli_box {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 20px;

        .xbbl_left,
        .xbbl_right {
          flex: 1;
        }

        .xbbl_center {
          flex: 1;
          text-align: center;
          color: #fff;

          span {
            font-size: 18px;
            display: block;
            margin-bottom: 10px;
          }

          p {
            font-size: 24px;
            font-weight: bold;

            .s-c-blue-gradient {
              color: #00c0ff;
              font-size: 32px;
            }
          }
        }

        .gender-chart {
          width: 220px;
          height: 300px;
        }
      }

      .nlfb_box {
        .age-chart {
          width: 100%;
          height: 400px;
        }
      }

      .xfsp_box {
        .consume-chart {
          width: 100%;
          height: 400px;
        }
      }
    }
  }
}
</style>
