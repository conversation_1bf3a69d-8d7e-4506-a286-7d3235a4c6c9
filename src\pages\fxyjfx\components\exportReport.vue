<!--
 * @Description: 预警详情弹框
 * @Version: 1.0
 * @Autor: wjb
 * @Date: 2025-09-25 16:00:00
 * @LastEditors: wjb
 * @LastEditTime: 2025-09-28 16:48:38
-->
<template>
  <div class="export-content">
    <div class="form-container">
      <!-- 事件详情 -->
      <div class="form-section">
        <div class="form-row">
          <div class="form-item">
            <label class="form-label">数据来源</label>
            <el-select v-model="formData.dataSource" placeholder="请选择数据来源" class="form-select">
              <el-option label="统计数据" value="1"></el-option>
              <el-option label="实时数据" value="2"></el-option>
            </el-select>
          </div>
        </div>
        <div class="form-row" v-if="formData.dataSource === '实时数据'">
          <div class="form-item">
            <label class="form-label">统计时间</label>
            <el-date-picker
              v-model="formData.time"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              class="form-date-editor"
            ></el-date-picker>
          </div>
        </div>
        <div class="form-row">
          <div class="form-item">
            <label class="form-label">面向单位</label>
            <el-select v-model="formData.deptName" placeholder="请选择面向单位" class="form-select" @change="handleDeptChange">
              <el-option label="统计局" value="统计局"></el-option>
              <el-option label="公安" value="公安"></el-option>
              <el-option label="民政局" value="民政局"></el-option>
              <el-option label="卫健局" value="卫健局"></el-option>
              <el-option label="人社局" value="人社局"></el-option>
            </el-select>
          </div>
        </div>
        <div class="form-row">
          <div class="form-item">
            <label class="form-label">模板选择</label>
            <el-select v-model="formData.templateFlag" placeholder="请选择模板" class="form-select">
              <el-option label="市统计局人口分析报告模板" value="1"></el-option>
              <el-option label="市公安人口分析报告模板" value="2"></el-option>
              <el-option label="市民政局人口分析报告模板" value="3"></el-option>
              <el-option label="市卫健局人口分析报告模板" value="4"></el-option>
              <el-option label="市人社局人口分析报告模板" value="5"></el-option>
            </el-select>
          </div>
        </div>
        <div class="form-row">
          <div class="form-item">
            <label class="form-label"></label>
            <el-checkbox v-model="formData.sendFlag" class="form-checkbox">是否发送对应部门人员</el-checkbox>
          </div>
        </div>
      </div>

      <div style="text-align: center">
        <el-button style="width: 140px; font-size: 32px" @click="close">取消</el-button>
        <el-button type="primary" style="width: 140px; font-size: 32px" @click="submit">生成</el-button>
      </div>
    </div>
  </div>
</template>

<script>
import { addReport } from '@/api/csdnIndexApi'
export default {
  name: 'WarningDetailDialog',
  components: {},
  props: {},
  data() {
    return {
      formData: {
        dataSource: '', // 数据来源
        sendFlag: true
      },
      // 面向单位与模板的映射关系
      deptTemplateMap: {
        '市统计局': '1',
        '市公安': '2',
        '市民政局': '3',
        '市卫健局': '4',
        '市人社局': '5'
      }
    }
  },
  mounted() {},
  watch: {},
  methods: {
    close() {
      this.$emit('close')
    },
    // 面向单位改变时,自动选择对应模板
    handleDeptChange(value) {
      if (value && this.deptTemplateMap[value]) {
        this.formData.templateFlag = this.deptTemplateMap[value]
      }
    },
    submit(){
      // 验证数据来源
      if(!this.formData.dataSource){
        this.$message.warning('请选择数据来源')
        return
      }
      // 当数据来源为"实时数据"时,验证统计时间
      if(this.formData.dataSource === '实时数据'){
        if(this.formData.time == null || this.formData.time.length == 0){
          this.$message.warning('请选择统计时间')
          return
        }
      }
      if(!this.formData.deptName){
        this.$message.warning('请选择面向单位')
        return
      }
      if(!this.formData.templateFlag){
        this.$message.warning('请选择模板')
        return
      }
      let params={
        dataSource: this.formData.dataSource,
        deptName:this.formData.deptName,
        templateFlag:this.formData.templateFlag,
        sendFlag:this.formData.sendFlag?1:0
      }
      // 只有当数据来源为"实时数据"时才传递时间参数
      if(this.formData.dataSource === '实时数据' && this.formData.time){
        params.startTime = this.formData.time[0]
        params.endTime = this.formData.time[1]
      }
      addReport(params).then(res => {
        if(res.data.code == 200){
          this.$message.success('生成成功')
          this.$emit('addSuccess')
        }
      })
    }
  },

  beforeDestroy() {},
}
</script>

<style lang="less">
.export-content {
  height: 100%;
  background: rgba(0, 61, 61, 0.8);
  border-radius: 4px;
  padding: 40px !important;
  overflow-y: auto;

  .form-container {
    .form-section {
      margin-bottom: 50px;

      .section-header {
        color: #00cce3;
        font-size: 32px;
        font-weight: bold;
        margin-bottom: 30px;
        padding-bottom: 16px;
      }

      .form-row {
        display: flex;
        gap: 40px;
        margin-bottom: 30px;

        .form-item {
          flex: 1;

          &.full-width {
            flex: 1 1 100%;
          }

          .form-label {
            display: block;
            color: #fff;
            font-size: 28px;
            margin-bottom: 16px;
          }

          .form-select {
            width: 100%;

            .el-input__inner {
              background: rgba(0, 61, 61, 0.8);
              color: #fff;
              height: 72px !important;
              line-height: 72px !important;
              font-size: 28px;

              &:focus {
                border-color: #00cce3;
              }

              &::placeholder {
                color: rgba(255, 255, 255, 0.5);
              }
            }

            .el-input__suffix {
              .el-input__suffix-inner {
                .el-select__caret {
                  color: #00cce3;
                }
              }
            }
          }

          .form-input {
            width: 100%;

            .el-input__inner {
              background: rgba(0, 61, 61, 0.8);
              color: #fff;
              height: 72px !important;
              line-height: 72px !important;
              font-size: 28px;

              &:focus {
                border-color: #00cce3;
              }

              &::placeholder {
                color: rgba(255, 255, 255, 0.5);
              }
            }
          }
          .form-date-editor{
             width: 600px;

            &.el-input__inner {
              background: rgba(0, 61, 61, 0.8);
              color: #fff;
              height: 72px !important;
              line-height: 72px !important;
              font-size: 28px;

              .el-range-input{
                background: rgba(0, 61, 61, 0.8);
                font-size: 28px;
                color: #fff;
              }
               .el-range__close-icon{
                color: #fff !important;
               }
              .el-input__icon{
                width: 50px;
                font-size: 32px;
              }

              .el-range-separator{
                font-size: 28px;
                line-height: 56px;
                color: #fff;
              }
              

              &:focus {
                border-color: #00cce3;
              }

              &::placeholder {
                color: rgba(255, 255, 255, 0.5);
              }
            }
          }

          .form-checkbox{
            display: flex;
            align-items: center;
            .el-checkbox__inner{
              width: 28px;
              height: 28px;
            }
            .el-checkbox__inner::after{
              height:14px ;
              width: 6px;
              left: 8px;
              top: 2px;
            }
            .el-checkbox__label{
              font-size: 28px;
              color: #fff;
            }
          }
        }
      }
    }
  }
}
</style>
