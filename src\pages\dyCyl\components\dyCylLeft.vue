<template>
  <div class="container">
    <div class="titleWrap">
      <div class="title_l">
        <div class="title col-white">{{ currentTitle }}</div>
      </div>
      <div class="header-r">
        <div
          class="tab_item"
          v-for="(item, index) in tabList"
          @click="tabChange(index)"
          :class="tabIndex == index ? 'active' : ''"
          :key="index"
        >
          <span class="tfam" :class="tabIndex == index ? 'col-white' : 'col-gren'">{{ item }}</span>
        </div>
      </div>
    </div>

    <div class="header">
      <div class="header-l" v-show="tabIndex == 0">
        产业节点
        <count-to
          class="number s-c-yellow-gradient s-w7 newT"
          :start-val="0"
          :end-val="jd_num"
          :duration="1000"
        ></count-to>
        个
        <span style="margin-left: 100px"></span>
        企业数量
        <count-to
          class="number s-c-yellow-gradient s-w7 newT"
          :start-val="0"
          :end-val="qy_num"
          :duration="1000"
        ></count-to>
        家
      </div>

      <div class="header-c" v-show="false">
        <div v-for="(item, index) in legend" class="htabClass" :key="index">
          <span
            class="icon"
            :style="{ background: item.color2, border: '1px solid ' + item.color1, 'border-radius': '5px' }"
          ></span>
          {{ item.name }}
          <div class="countClass">
            {{ item.count }}
            <div class="unitCount">个</div>
          </div>
        </div>
      </div>
    </div>

    <div class="con">
      <div class="newChartWrap" v-show="tabIndex == 0">
        <div class="chartNewClass" id="chartNew"></div>
      </div>

      <div class="screenWrap" v-show="tabIndex == 1">
        <!-- 地图背景 -->
        <div class="map-bgnuo"></div>
        <!-- 地图 -->
        <div class="mapnuo" ref="myEchartnuo"></div>
        <!-- 地图描述 -->
        <div class="mapDesClass" v-show="cylId == 103 && showMapDesc"></div>
      </div>

      <div class="screenWrap positionClass" v-show="tabIndex == 2">
        <!-- 招商地图内容 -->
      </div>
    </div>
  </div>
</template>

<script>
import axios from 'axios'
import * as echarts from 'echarts'
import { getCsdnInterface1 } from '@/api/csdnIndexApi'

// 全局变量声明，和 Center.vue 保持一致
const baseURL = window.baseURL || { url: '' }

export default {
  name: 'DyCylLeft',
  data() {
    return {
      title: '',
      jd_num: 0,
      qy_num: 0,
      legend: [
        {
          name: '弱链',
          color1: '#F06060',
          color2: '#4D3958',
          count: '',
        },
        {
          name: '强链',
          color1: '#0091FF',
          color2: '#074C8C',
          count: '',
        },
        // {
        //   name: '断链',
        //   color1: '#13C2C2',
        //   color2: '#0E5D7B',
        //   count: '',
        // },
      ],
      cylOptions: [
        { id: 2001, name: '食品加工领域' },
        // { id: 1001, name: '金属制造领域', hasSubMenu: true },
        { id: 1001, name: '金属制造领域' },
        { id: 3001, name: '光电子信息领域' },
      ],
      list: [
        {
          name: '上游',
          jd: 0,
          qy: 0,
        },
        {
          name: '中游',
          jd: 0,
          qy: 0,
        },
        {
          name: '下游',
          jd: 0,
          qy: 0,
        },
      ],
      cylId: 101,
      tabIndex: 0,
      tabList: ['产业链图谱'],
      // tabList: ['产业链图谱', '产业地图', '招商地图'],
      myChartMapnuo: null,
      popData: [],
      popData3: [
        {
          name: '婺城区',
          value: [119.502579, 29.000607, 0],
        },
        {
          name: '金东区',
          value: [119.781264, 29.105635, 0],
        },
        {
          name: '兰溪市',
          value: [119.360521, 29.210065, 0],
        },
        {
          name: '东阳市',
          value: [120.33234, 29.222506, 0],
        },
        {
          name: '义乌市',
          value: [120.074311, 29.306463, 0],
        },
        {
          name: '永康市',
          value: [120.036328, 28.855293, 0],
        },
        {
          name: '浦江县',
          value: [119.893363, 29.551254, 0],
        },
        {
          name: '武义县',
          value: [119.819159, 28.796503, 0],
        },
        {
          name: '磐安县',
          value: [120.54113, 28.952227, 0],
        },
        {
          name: '开发区',
          value: [119.652577, 29.082627, 0],
        },
      ],
      basicInfo: {},
      showMapDesc: false,
      chartData0: [],
      chartData1: [],
      chartData2: [],
    }
  },
  computed: {
    height_2() {
      switch (this.cylId) {
        case 104:
          return '990px'
        case 103:
          return '990px'
        default:
          return '1530px'
      }
    },
    currentTitle() {
      const option = this.cylOptions.find((item) => item.id === this.cylId)
      return option ? option.name : '智能家电'
    },
  },
  mounted() {
    let that = this
    that.title = '2222'
    // 从路由参数中获取id，如果没有则默认为104
    that.cylId = this.$route.query.id ? parseInt(this.$route.query.id) : 104
    that.init1()
    this.getBsicInfo()
    if (this.tabIndex == 1) {
      this.initData()
    }
  },
  watch: {
    // 监听路由变化，当id参数改变时重新获取数据
    '$route.query.id'(newId) {
      if (newId) {
        this.cylId = parseInt(newId)
        this.init1()
        this.getBsicInfo()
        if (this.tabIndex == 1) {
          this.initData()
        }
      }
    },
  },
  methods: {
    init1() {
      this.queryNum()
      if (this.cylId == 104 || this.cylId == 1001 || this.cylId == 2001 || this.cylId == 3001 || this.cylId == 213) {
        // 先获取统计数据更新图例，再获取图表数据
        this.statisticsData().then(() => {
          this.querychartData()
        })
      } else {
        this.querychartDataOld()
      }
    },

    tabChange(index) {
      this.tabIndex = index
      if (index == 1) {
        // this.initData()
      }
    },

    queryNum() {
      if (this.cylId !== 109) {
        // 109人工智能产业链不用这个接口
        getCsdnInterface1('csdn_qyhx21', { ywwd2: this.cylId }).then((res) => {
          const data = (res && res.data && res.data.data) || []
          this.list.forEach((item) => {
            const found = data.find((el) => el.ywwd1 == item.name)
            item.qy = found ? found.qy_num || 0 : 0
            item.jd = found ? found.jd_num || 0 : 0
          })
        })
        getCsdnInterface1('csdn_qyhx22', { ywwd2: this.cylId }).then((res) => {
          const d = (res && res.data && res.data.data) || []
          this.jd_num = d[0] ? d[0].jd_num || 0 : 0
          this.qy_num = d[0] ? d[0].qy_num || 0 : 0
        })
      } else {
        getCsdnInterface1('csdn_qyhx38', { ywwd2: this.cylId }).then((res) => {
          const data = (res && res.data && res.data.data) || []
          this.list.forEach((item) => {
            const f = data.find((el) => el.location == item.name)
            item.qy = f ? f.qy_sum || 0 : 0
            item.jd = f ? f.jd_sum || 0 : 0
          })
        })
        getCsdnInterface1('csdn_qyhx40', { ywwd2: this.cylId }).then((res) => {
          const d = (res && res.data && res.data.data) || []
          this.jd_num = d[0] ? d[0].jd_num || 0 : 0
          this.qy_num = d[0] ? d[0].qy_num || 0 : 0
        })
      }
    },

    querychartDataOld() {
      getCsdnInterface1('csdn_qyhx1', { sstp: this.cylId }).then((res) => {
        const data = (res && res.data && res.data.data) || []
        getCsdnInterface1('csdn_qyhx20', { ywwd2: this.cylId }).then((el) => {
          const elData = (el && el.data && el.data.data) || []
          if (this.cylId !== 109) {
            // 109人工智能产业链不用这个接口的tjz
            data.forEach((item) => {
              item.count = 0
              elData.forEach((element) => {
                if (item.ej == element.ywwd1) {
                  item.count = Number(element.tjz)
                }
              })
            })
          }
          let data1 = data.filter((item) => {
            return item.pid == 0
          })
          let data2 = data.filter((item) => {
            return item.pid != 0
          })

          let result = []
          data1.forEach((item1) => {
            let bCount = 0
            let obj1 = {
              name: item1.ej,
              type: item1.aCount == 1 ? 1 : item1.aCount > 1 ? 2 : 0, // 0:弱链 1:强链 2:断链
              aCount: item1.aCount,
              location: item1.location,
              children: [],
            }
            data2.forEach((item2) => {
              if (item1.id == item2.pid) {
                let obj2 = {
                  name: item2.ej,
                  type: item2.count == 1 ? 1 : item2.count > 1 ? 2 : 0, // 0:断链 1:弱链 2:强链
                  num: item2.count,
                }
                obj1.children.push(obj2)
                bCount += item2.count
              }
            })
            obj1.type = bCount == 1 ? 1 : bCount > 1 ? 2 : 0 // 0:断链 1:弱链 2:强链
            result.push(obj1)
          })

          this.chartData0 = result.filter((item) => {
            return item.location == '上游'
          })
          this.chartData1 = result.filter((item) => {
            return item.location == '中游'
          })
          this.chartData2 = result.filter((item) => {
            return item.location == '下游'
          })

          this.$nextTick(() => {
            this.getchart('chart1_0', this.chartData0, 0)
            this.getchart('chart1_1', this.chartData1, 1)
            this.getchart('chart1_2', this.chartData2, 2)
          })
        })
      })
    },

    querychartData() {
      getCsdnInterface1('csdn_lqzf_cly', { sscly: this.cylId }).then((res) => {
        const data = (res && res.data && res.data.data) || []
        let treeList = this.listToTree(data)
        console.log('1111', treeList)

        // 获取断链、弱链、强链数据并合并到树形数据中
        getCsdnInterface1('qyhx_cyl_qrdlds', { sscly: this.cylId }).then((linkRes) => {
          const linkData = (linkRes && linkRes.data && linkRes.data.data) || []
          // 将链路类型数据合并到树形数据中
          // this.mergeLinkDataToTree(treeList, linkData)

          this.$nextTick(() => {
            this.getchartnewTwo('chartNew', treeList)
          })
        })
      })
    },

    statisticsData() {
      return getCsdnInterface1('qyhx_cyl_qrdlds', { sscly: this.cylId }).then((res) => {
        const data = (res && res.data && res.data.data) || []

        // 动态更新图例名称和统计数据
        data.forEach((el, index) => {
          if (index < this.legend.length) {
            this.legend[index].name = el.ldlx
            this.legend[index].count = el.tjz
          }
        })
      })
    },

    handleData(list) {
      let newlist = list.map((item) => {
        let innerChildren = []
        if (item.children && item.children.length > 0) {
          innerChildren = this.handleData(item.children)
        }
        return {
          name: item.ld_name,
          value: item.jdqys,
          jdqys: item.jdqys,
          ld_code: item.ld_code,
          ldcj: item.ldcj,
          ldlx: item.ldlx,
          parent_code: item.parent_code,
          sscly: item.sscly,
          children: innerChildren,
        }
      })
      return newlist
    },

    getchartnewTwo(id, chartData) {
      const myChart = echarts.init(document.getElementById(id))
      const newData = this.handleData(chartData)
      console.log('222', newData)
      const option = {
        tooltip: {
          trigger: 'item',
          triggerOn: 'mousemove',
          formatter: (p) => `${p.data.name} ${p.data.value}`,
          textStyle: { color: '#fff', fontSize: 36 },
          backgroundColor: '#041b34',
          borderColor: '#035693',
          borderWidth: 8,
          padding: [20, 30, 20, 30],
        },
        dataZoom: { type: 'inside' },
        series: [
          {
            type: 'tree',
            roam: false, // 禁用拖拽和缩放，防止图表吸附问题
            initialTreeDepth: 1,
            data: newData,
            top: '0%',
            left: '20%',
            bottom: '0%',
            right: '0%',
            symbolSize: 50,
            zoom: 0.8,
            edgeShape: 'polyline', //链接线是折现还是曲线
            itemStyle: { color: 'rgba(255,255,255,0)' },
            symbol: (_value, params) => {
              // console.log('图标', params)
              let icon = ''
              if (params.data.children.length < 1) {
                icon = ''
              } else if (params.collapsed) {
                icon = `image://data:image/png;base64,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`
              } else if (!params.collapsed) {
                icon = `image://data:image/png;base64,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`
              }
              return icon
            },
            itemStyle: { color: 'rgba(255,255,255,0)' },
            symbolOffset: [20, 0],
            label: {
              normal: {
                position: 'left',
                verticalAlign: 'middle',
                distance: -2,
                overflow: 'truncate',
                ellipsis: '...',
                color: '#fff',
                formatter: (p) => {
                  let str = `${p.data.name} ${p.data.value == 0 ? '' : p.data.value}`
                  if (str.length > 8) str = str.slice(0, 8) + '...'
                  let menu = `{d|${str}}`

                  // 动态匹配图例中的ldlx值来设置样式
                  const legendIndex = this.legend.findIndex((item) => item.name === p.data.ldlx)
                  if (legendIndex === 0) menu = `{a|${str}}`
                  else if (legendIndex === 1) menu = `{b|${str}}`
                  else if (legendIndex === 2) menu = `{c|${str}}`

                  return menu
                },
                rich: {
                  a: {
                    fontSize: 36,
                    width: 360,
                    height: 80,
                    color: '#fff',
                    align: 'middle',
                    backgroundColor: '#492c42',
                    borderRadius: 10,
                    overflow: 'truncate',
                    ellipsis: '...',
                    borderWidth: 3,
                    borderColor: '#e85e5f',
                  },
                  b: {
                    fontSize: 36,
                    width: 360,
                    height: 80,
                    color: '#fff',
                    align: 'middle',
                    backgroundColor: '#04457c',
                    borderRadius: 10,
                    overflow: 'truncate',
                    ellipsis: '...',
                    borderWidth: 3,
                    borderColor: '#026dc1',
                  },
                  c: {
                    fontSize: 36,
                    width: 360,
                    height: 80,
                    color: '#fff',
                    align: 'middle',
                    backgroundColor: '#095269',
                    borderRadius: 10,
                    overflow: 'truncate',
                    ellipsis: '...',
                    borderWidth: 3,
                    borderColor: '#11aaaf',
                  },
                  d: {
                    fontSize: 36,
                    width: 360,
                    height: 80,
                    color: '#fff',
                    align: 'middle',
                    backgroundColor: '#395372',
                    borderRadius: 10,
                    overflow: 'truncate',
                    ellipsis: '...',
                    borderWidth: 3,
                    borderColor: '#bad3ff',
                  },
                },
              },
            },
            lineStyle: { color: 'rgba(255,255,255,.5)', width: 5, curveness: 0.5 },
            expandAndCollapse: true,
            animationDuration: 550,
            animationDurationUpdate: 750,
          },
        ],
      }
      myChart.setOption(option)

      // 添加一个标志来跟踪是否正在处理弹窗
      let isProcessingDialog = false

      // 添加全局鼠标事件监听器来防止图表吸附
      const handleGlobalMouseMove = (event) => {
        if (isProcessingDialog) {
          // 如果正在处理弹窗，确保图表不会吸附
          const chartDom = myChart.getDom()
          if (chartDom) {
            chartDom.style.cursor = 'default'
          }
        }
      }

      const handleGlobalMouseUp = () => {
        if (isProcessingDialog) {
          // 全局鼠标抬起时重置图表状态
          this.preventChartDrag(myChart)
        }
      }

      // 绑定全局事件
      document.addEventListener('mousemove', handleGlobalMouseMove)
      document.addEventListener('mouseup', handleGlobalMouseUp)

      // 在组件销毁时清理事件监听器
      this.$once('hook:beforeDestroy', () => {
        document.removeEventListener('mousemove', handleGlobalMouseMove)
        document.removeEventListener('mouseup', handleGlobalMouseUp)
      })

      myChart.on('mousedown', (e) => {
        // 如果正在处理弹窗，直接返回
        if (isProcessingDialog) {
          return
        }

        // 阻止默认行为，防止ECharts进入拖拽状态
        if (e.event && e.event.event) {
          e.event.event.preventDefault()
          e.event.event.stopPropagation()
        }

        const name = e.data && e.data.name
        const nodeList = myChart._chartsViews[0]._data.tree._nodes
        const curNode = nodeList.filter((item) => {
          return item.name === name && item.children.length == e.data.children.length
        })

        const dataIndex = curNode[0].dataIndex
        const curIsExpand = JSON.parse(JSON.stringify(curNode[0].isExpand))
        if (e.event.topTarget && e.event.topTarget.style && e.event.topTarget.style.text) {
          // 设置处理标志
          isProcessingDialog = true

          // 叶子节点：按企业名称查询并弹出企业详情
          if (!e.data.children || e.data.children.length === 0) {

            // 立即重置拖拽状态，防止图表吸附
            this.preventChartDrag(myChart)
            this.searchEnterpriseAndEmit(name, e)

            // 延迟重置标志
            setTimeout(() => {
              isProcessingDialog = false
            }, 500)
            return
          }

          // 非叶子节点：仍做展开/收起处理
          nodeList.forEach((item) => {
            if (item.dataIndex == dataIndex && item.name == name && !curIsExpand) {
              item.isExpand = true
            } else if (item.dataIndex == dataIndex && item.name == name && curIsExpand) {
              item.isExpand = false
            }
          })

          // 非叶子节点保留原有弹窗逻辑
          // 立即重置拖拽状态，防止图表吸附
          this.preventChartDrag(myChart)
          this.triggerEnterpriseDialog(name, e.data)

          // 延迟重置标志
          setTimeout(() => {
            isProcessingDialog = false
          }, 500)
        }
      })
    },

    getchart(id, chartData, index) {
      const myChart = echarts.init(document.getElementById(id))
      chartData.forEach((item) => {
        item.itemStyle = { color: this.legend[item.type].color2, borderColor: this.legend[item.type].color1 }
        if (item.children) {
          item.children.forEach((el) => {
            el.itemStyle = { color: this.legend[el.type].color2, borderColor: this.legend[el.type].color1 }
          })
        }
      })
      const data = { name: '', lineStyle: { width: 0 }, children: chartData }
      const format = (name) => (name.length > 6 ? name.slice(0, 6) + '...' : name)
      const option = {
        series: [
          {
            type: 'tree',
            initialTreeDepth: -1,
            data: [data],
            top: '0%',
            left: '-30%',
            bottom: '0%',
            right: '25%',
            symbolSize: [300, 50],
            edgeShape: 'polyline',
            symbol: 'rect',
            label: {
              normal: {
                verticalAlign: 'middle',
                align: 'center',
                fontSize: 32,
                color: '#fff',
                borderRadius: [0, 0, 5, 5],
                formatter: (p) => (p.data.num ? `${format(p.data.name)}(${p.data.num})` : format(p.data.name)),
              },
            },
            lineStyle: { color: 'rgba(255,255,255,0.8)', width: 3, curveness: 0.5 },
            expandAndCollapse: true,
            animationDuration: 550,
            animationDurationUpdate: 750,
          },
        ],
      }
      myChart.setOption(option)
      myChart.on('click', (params) => {
        if (!params.data.children) {
          // 叶子节点：按企业名称查询并弹出企业详情
          // 让图表失去焦点
          this.blurChart(myChart)
          this.searchEnterpriseAndEmit(params.name)
        }
      })
    },

    queryQyList(name) {
      console.log('queryQyList')
      if (window.parent && window.parent.lay && window.parent.lay.openIframe) {
        const that = this
        window.parent.lay.openIframe({
          type: 'openIframe',
          name: 'gf-qy-list',
          src: baseURL.url + '/static/citybrain/qyhx/commont/gf-qy-list.html',
          left: 'calc(50% - 1554px)',
          top: '190px',
          width: '3108px',
          height: '1900px',
          zIndex: '500',
          argument: { status: 'qyList', name, cylId: that.cylId },
        })
      }
    },

    getGeoJon(code, name) {
      axios({ method: 'get', url: `https://geo.datav.aliyun.com/areas_v3/bound/${code}.json` }).then((res) => {
        this.myChartMapnuo = echarts.init(this.$refs.myEchartnuo).dispose()
        this.myChartMapnuo = echarts.init(this.$refs.myEchartnuo)
        echarts.registerMap(name, res.data)
        this.showMapDesc = false
        setTimeout(() => {
          this.initEcharts(name, res.data)
        }, 500)
      })
    },

    initEcharts(registname, registdata) {
      this.myChartMapnuo.resize()
      this.$nextTick(() => {
        const option = {
          geo: [
            {
              show: true,
              map: registname,
              zoom: registname == '中国' ? 1.3 : 1.2,
              roam: false,
              zlevel: 5,
              layoutCenter: ['50%', '50%'],
              layoutSize: '90%',
              aspectScale: 0.85,
              itemStyle: { areaColor: 'transparent' },
              silent: true,
            },
            {
              show: true,
              map: registname,
              zoom: registname == '中国' ? 1.3 : 1.2,
              roam: false,
              zlevel: 4,
              layoutCenter: ['50.4%', '50%'],
              layoutSize: '90%',
              aspectScale: 0.85,
              itemStyle: {
                borderWidth: 1,
                borderColor: 'rgba(22, 186, 212,0.8)',
                shadowColor: 'rgba(80, 183, 140,0.5)',
                shadowOffsetY: 5,
                shadowBlur: 15,
                areaColor: 'rgba(5,21,35,0.1)',
              },
              silent: true,
            },
            {
              show: true,
              map: registname,
              zoom: registname == '中国' ? 1.3 : 1.2,
              roam: false,
              zlevel: 3,
              layoutCenter: ['50.6%', '50%'],
              layoutSize: '90%',
              aspectScale: 0.85,
              itemStyle: {
                borderWidth: 6,
                borderColor: 'rgba(29,111,165,1)',
                shadowColor: 'rgba(29,111,165,0.5)',
                shadowOffsetY: 15,
                shadowBlur: 8,
                areaColor: 'rgba(5,21,35,0.8)',
              },
              silent: true,
            },
            {
              show: true,
              map: registname,
              zoom: registname == '中国' ? 1.3 : 1.2,
              roam: false,
              zlevel: 3,
              layoutCenter: ['50.8%', '50%'],
              layoutSize: '90%',
              aspectScale: 0.85,
              itemStyle: {
                borderWidth: 6,
                borderColor: 'rgba(29,111,165,1)',
                shadowColor: 'rgba(29,111,165,0.5)',
                shadowOffsetY: 15,
                shadowBlur: 8,
                areaColor: 'rgba(5,21,35,0.8)',
              },
              silent: true,
            },
            {
              show: true,
              map: registname,
              zoom: registname == '中国' ? 1.3 : 1.2,
              roam: false,
              zlevel: 3,
              layoutCenter: ['51%', '50%'],
              layoutSize: '90%',
              aspectScale: 0.85,
              itemStyle: {
                borderWidth: 6,
                borderColor: 'rgba(29,111,165,1)',
                shadowColor: 'rgba(29,111,165,0.5)',
                shadowOffsetY: 15,
                shadowBlur: 8,
                areaColor: 'rgba(5,21,35,0.8)',
              },
              silent: true,
            },
          ],
          series: [
            {
              type: 'map',
              map: registname,
              zoom: registname == '中国' ? 1.3 : 1.2,
              roam: false,
              layoutCenter: ['50%', '50%'],
              layoutSize: '90%',
              aspectScale: 0.85,
              selectedMode: false,
              label: { show: true, color: '#fff', position: 'inside', distance: 0, fontSize: 35 },
              itemStyle: {
                normal: {
                  areaColor: '#0c395d',
                  borderColor: '#56c4e3',
                  borderWidth: 2,
                  shadowBlur: 15,
                  shadowColor: 'rgb(58,115,192)',
                  shadowOffsetX: 7,
                  shadowOffsetY: 6,
                },
                emphasis: { color: '#000', areaColor: '#8dd7fc' },
              },
              zlevel: 99,
              data: [],
            },
            {
              type: 'effectScatter',
              coordinateSystem: 'geo',
              zlevel: 999,
              data: this.popData,
              symbolSize: 10,
              label: {
                normal: {
                  show: true,
                  formatter: (p) => `{fline|${p.name}}{tline|${p.value[2]}}`,
                  position: 'top',
                  backgroundColor: '#0a0b0b99',
                  padding: [0, 0],
                  borderRadius: 3,
                  fontSize: 32,
                  fontWeight: 500,
                  color: '#ffffff',
                  rich: {
                    fline: {
                      padding: [10, 10, 10, 10],
                      color: '#ffffff',
                      fontSize: 32,
                      width: 80,
                      align: 'center',
                      height: 40,
                      backgroundColor: '#ff7817',
                    },
                    tline: {
                      padding: [10, 10, 10, 10],
                      color: '#ffffff',
                      fontSize: 32,
                      lineHeight: 45,
                      width: 80,
                      height: 40,
                    },
                  },
                },
                emphasis: { show: true },
              },
              itemStyle: { color: '#ff7817' },
            },
          ],
        }
        this.myChartMapnuo.setOption(option)
        this.showMapDesc = true
      })
    },

    initData() {
      this.popData = []
      this.popData3.map((item) => (item.value[2] = 0))
      getCsdnInterface1('csdn_qyhx16', { ywwd2: this.cylId }).then((res) => {
        const data = (res && res.data && res.data.data) || []
        data.map((item) => {
          const t = this.popData3.find((el) => item.qxwd == el.name)
          if (t) t.value[2] = item.tjz
        })
        this.popData = this.popData3.filter((item) => item.value[2] != 0)
        this.getGeoJon('330700_full', '金华市')
      })
    },

    getBsicInfo() {
      getCsdnInterface1('csdn_qyhx44', { sstp: this.cylId }).then((res) => {
        const d = (res && res.data && res.data.data) || []
        this.basicInfo = d[0] || {}
      })
    },

    listToTree(list) {
      const map = {}
      const tree = []
      list.forEach((item) => {
        map[item.ld_code] = { ...item, children: [] }
      })
      list.forEach((item) => {
        const node = map[item.ld_code]
        if (item.parent_code && map[item.parent_code]) map[item.parent_code].children.push(node)
        else tree.push(node)
      })
      return tree
    },

    // 将断链、弱链、强链数据合并到树形数据中
    mergeLinkDataToTree(treeList, linkData) {
      // 由于qyhx_cyl_qrdlds接口主要返回统计数据，而不是具体的节点链路类型
      // 这里我们可以根据业务逻辑为节点分配链路类型
      // 或者等待后端提供具体的节点链路类型映射数据

      // 递归遍历树形数据，为每个节点添加链路类型
      const traverseTree = (nodes, depth = 0) => {
        nodes.forEach((node, index) => {
          // 这里可以根据业务逻辑设置链路类型
          // 例如：根据节点层级、企业类型等来判断
          if (node.ldlx === '主导企业') {
            // 主导企业可能是强链
            node.ldlx = this.legend[2]?.name || '强链'
          } else if (node.ldlx === '配套企业') {
            // 配套企业可能是弱链
            node.ldlx = this.legend[1]?.name || '弱链'
          } else if (depth === 0) {
            // 根节点可能是断链
            node.ldlx = this.legend[0]?.name || '断链'
          }

          // 递归处理子节点
          if (node.children && node.children.length > 0) {
            traverseTree(node.children, depth + 1)
          }
        })
      }

      traverseTree(treeList)
    },

    // 触发企业信息弹窗事件
    triggerEnterpriseDialog(name, nodeData) {
      const dialogData = {
        name: name,
        ssjd: name, // 所属节点
        sstp: this.cylId, // 所属图谱
        status: 'qyList',
        nodeData: nodeData, // 传递节点数据，包含链路类型信息
      }
      // 向父组件发送事件
      this.$emit('show-enterprise-dialog', dialogData)
    },

    // 根据节点名称搜索企业并以 openDialog 事件弹出详情
    searchEnterpriseAndEmit(name, e) {
      const params = { name }
      getCsdnInterface1('qyhx_qyxx_search_al', params).then((res) => {
        const arr = (res && res.data && res.data.data) || []
        const matched = arr.filter((o) => o.qymc === name)
        const item = matched[0] || arr[0]
        if (item) {
          this.$emit('openDialog', [item])
        } else {
          // if (this.$message) this.$message.warning('未查询到相关企业')
          // else console.warn('未查询到相关企业', name)
          // 非叶子节点保留原有弹窗逻辑
          this.triggerEnterpriseDialog(name, e.data)
        }
      })
    },

    // 处理节点右击事件
    handleNodeRightClick(name, nodeData, event) {
      console.log('处理右击事件:', { name, nodeData, event })

      // 特定企业节点的处理
      const specialEnterprises = ['湖北金禄科技有限公司', '湖北爱仕达电器有限公司']

      if (specialEnterprises.includes(name)) {
        // 对特定企业显示招引清单弹窗
        this.triggerEnterpriseDialog(name, nodeData)
        return
      }

      // 获取鼠标位置
      const x = event.clientX
      const y = event.clientY

      // 其他节点显示自定义右键菜单
      this.showContextMenu(name, nodeData, x, y)
    },

    // 显示右键菜单
    showContextMenu(name, nodeData, x, y) {
      // 示例：创建一个简单的右键菜单
      const contextMenu = document.createElement('div')
      contextMenu.style.position = 'fixed'
      contextMenu.style.left = x + 'px'
      contextMenu.style.top = y + 'px'
      contextMenu.style.backgroundColor = '#041b34'
      contextMenu.style.border = '2px solid #035693'
      contextMenu.style.borderRadius = '8px'
      contextMenu.style.padding = '10px'
      contextMenu.style.zIndex = '9999'
      contextMenu.style.color = '#fff'
      contextMenu.style.fontSize = '14px'
      contextMenu.style.minWidth = '150px'

      // 添加菜单项
      const menuItems = [
        { text: '查看详情', action: () => this.viewNodeDetail(name, nodeData) },
        { text: '展开/收起', action: () => this.toggleNodeExpand(name, nodeData) },
        { text: '复制节点名称', action: () => this.copyNodeName(name) },
      ]

      menuItems.forEach((item) => {
        const menuItem = document.createElement('div')
        menuItem.textContent = item.text
        menuItem.style.padding = '8px 12px'
        menuItem.style.cursor = 'pointer'
        menuItem.style.borderRadius = '4px'
        menuItem.style.transition = 'background-color 0.2s'

        menuItem.addEventListener('mouseenter', () => {
          menuItem.style.backgroundColor = '#035693'
        })

        menuItem.addEventListener('mouseleave', () => {
          menuItem.style.backgroundColor = 'transparent'
        })

        menuItem.addEventListener('click', () => {
          item.action()
          document.body.removeChild(contextMenu)
        })

        contextMenu.appendChild(menuItem)
      })

      // 添加到页面
      document.body.appendChild(contextMenu)

      // 点击其他地方关闭菜单
      const closeMenu = (e) => {
        if (!contextMenu.contains(e.target)) {
          document.body.removeChild(contextMenu)
          document.removeEventListener('click', closeMenu)
        }
      }

      setTimeout(() => {
        document.addEventListener('click', closeMenu)
      }, 100)
    },

    // 查看节点详情
    viewNodeDetail(name, nodeData) {
      console.log('查看节点详情:', name, nodeData)
      // 可以触发详情弹窗或其他操作
      this.searchEnterpriseAndEmit(name)
    },

    // 切换节点展开/收起状态
    toggleNodeExpand(name, nodeData) {
      console.log('切换节点展开状态:', name, nodeData)
      // 这里可以添加展开/收起的逻辑
    },

    // 复制节点名称
    copyNodeName(name) {
      if (navigator.clipboard) {
        navigator.clipboard.writeText(name).then(() => {
          if (this.$message) {
            this.$message.success('节点名称已复制到剪贴板')
          } else {
            console.log('节点名称已复制:', name)
          }
        })
      } else {
        // 兼容旧浏览器
        const textArea = document.createElement('textarea')
        textArea.value = name
        document.body.appendChild(textArea)
        textArea.select()
        document.execCommand('copy')
        document.body.removeChild(textArea)

        if (this.$message) {
          this.$message.success('节点名称已复制到剪贴板')
        } else {
          console.log('节点名称已复制:', name)
        }
      }
    },

    // 让图表失去焦点
    blurChart(chart) {
      try {
        // 获取图表的DOM元素
        const chartDom = chart.getDom()
        if (chartDom) {
          // 温和地移除焦点，避免干扰ECharts内部状态
          chartDom.blur()

          // 重置可能的拖拽状态
          this.resetChartDragState(chart)

          // 创建一个临时的不可见按钮来接收焦点
          const tempButton = document.createElement('button')
          tempButton.style.position = 'fixed'
          tempButton.style.left = '-9999px'
          tempButton.style.top = '-9999px'
          tempButton.style.width = '1px'
          tempButton.style.height = '1px'
          tempButton.style.opacity = '0'
          tempButton.style.pointerEvents = 'none'
          tempButton.setAttribute('aria-hidden', 'true')
          document.body.appendChild(tempButton)

          // 让临时按钮获得焦点
          tempButton.focus()

          // 延迟移除临时按钮
          setTimeout(() => {
            if (document.body.contains(tempButton)) {
              document.body.removeChild(tempButton)
            }
          }, 300)
        }
      } catch (error) {
        console.warn('图表失去焦点失败:', error)
      }
    },

    // 重置图表拖拽状态
    resetChartDragState(chart) {
      try {
        // 触发一个mouseup事件来重置可能的拖拽状态
        const chartDom = chart.getDom()
        if (chartDom) {
          const mouseUpEvent = new MouseEvent('mouseup', {
            bubbles: true,
            cancelable: true,
            view: window,
          })
          chartDom.dispatchEvent(mouseUpEvent)
        }
      } catch (error) {
        console.warn('重置图表拖拽状态失败:', error)
      }
    },

    // 防止图表拖拽吸附
    preventChartDrag(chart) {
      try {
        const chartDom = chart.getDom()
        if (chartDom) {
          // 立即触发多个事件来彻底重置拖拽状态
          const events = ['mouseup', 'mouseleave', 'mouseout']
          events.forEach((eventType) => {
            const event = new MouseEvent(eventType, {
              bubbles: false, // 阻止冒泡，防止触发全局mouseup事件导致无限递归
              cancelable: true,
              view: window,
              clientX: 0,
              clientY: 0,
            })
            chartDom.dispatchEvent(event)
          })

          // 清除可能的拖拽相关样式和状态
          chartDom.style.cursor = 'default'
          chartDom.style.userSelect = 'none'

          // 移除可能的拖拽类名
          chartDom.classList.remove('echarts-dragging')

          // 强制重置图表的内部状态
          if (chart._zr && chart._zr.handler) {
            // 重置zrender的拖拽状态
            chart._zr.handler._draggingMgr && (chart._zr.handler._draggingMgr._dragging = false)
            chart._zr.handler._mouseMoveHandler &&
              chart._zr.handler._mouseMoveHandler.reset &&
              chart._zr.handler._mouseMoveHandler.reset()
          }

          // 延迟重新渲染图表以确保状态完全重置
          setTimeout(() => {
            try {
              chart.resize()
              // 再次确保没有拖拽状态
              chartDom.style.cursor = 'default'
            } catch (e) {
              console.warn('图表重置时出现错误:', e)
            }
          }, 100)
        }
      } catch (error) {
        console.warn('防止图表拖拽失败:', error)
      }
    },
  },
}
</script>

<style lang="less" scoped>
* {
  margin: 0;
  padding: 0;
}

[v-cloak] {
  display: none;
}

.container {
  width: 1818px;
  margin-left: 50px;
  height: 1862px;
  padding: 10px 55px 0px;
  box-sizing: border-box;
  background: url('@/assets/img/dyCyl/cyl_new_bg.png') no-repeat;
  background-size: 100% 100%;
}

.title {
  /* height: 150px; */
  font-weight: bolder;
  font-size: 72px;
  /* line-height: 150px; */
  letter-spacing: 7px;
}

.info {
  display: flex;
  flex-wrap: wrap;
  font-size: 40px;
  margin: 40px 20px;
  line-height: 80px;
}

.info > div {
  width: 33%;
}

.info-name {
  color: #b7c2cb;
  font-size: 40px;
}

.info-value {
  color: #fff;
  font-size: 40px;
  font-weight: bolder;
}

.col-white {
  background: linear-gradient(90deg, #ffffff 0%, #bcecff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.col-gren {
  background: linear-gradient(90deg, #ffffff 0%, #cae2ff 73%, #8facd7 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.header {
  width: 100%;
  height: 150px;
  /* background: url('@/assets/img/dyCyl/title-bg.png') no-repeat;
  background-size: 100% 100%; */
  display: flex;
  justify-content: space-between;
  line-height: 150px;
  padding: 0px 50px;
  box-sizing: border-box;
}

.number {
  display: inline-block;
  font-size: 72px;
  margin: 0 10px;
}

.header-l {
  display: flex;
  font-size: 36px;
  color: #bad3ff;
}

.header-c {
  width: 916px;
  font-size: 36px;
  color: #bad3ff;
  display: flex;
  justify-content: space-evenly;
}

.header-c .icon {
  width: 26px;
  height: 26px;
  display: inline-block;
  margin-right: 10px;
}

.header-r {
  display: flex;
  align-items: center;
  text-align: center;
}

.header-r > img {
  cursor: pointer;
  margin-top: 35px;
}

.con {
  display: flex;
  justify-content: space-evenly;
  height: 1520px;
  margin-top: 20px;
  font-size: 36px;
  color: #bad3ff;
}

.con-item {
  /* width: 900px; */
  width: 925px;
  height: 100%;
  background: rgba(26, 85, 191, 0.12);
}

.con-title {
  width: 100%;
  height: 91px;
  line-height: 91px;
  background: url(@/assets/img/dyCyl/title-2.png) no-repeat;
  background-size: 100% 100%;
  padding: 0 85px;
  box-sizing: border-box;
  display: flex;
  justify-content: space-between;
  font-size: 36px;
}

.chart_box {
  height: calc(100% - 90px);
  overflow-y: scroll;
  overflow-x: hidden;
}

/* 设置滚动条的样式 */
::-webkit-scrollbar {
  width: 10px;
}

/* 滚动槽 */
::-webkit-scrollbar-track {
  border-radius: 5px;
}

/* 滚动条滑块 */
::-webkit-scrollbar-thumb {
  border-radius: 10px;
  background: rgba(35, 144, 207, 0.4);
}

.tab_item {
  width: 314px;
  height: 75px;
  background: url('@/assets/img/dyCyl/btn2.png') no-repeat;
  background-size: 100% 100%;
  font-size: 46px;
  text-align: center;
  line-height: 75px;
  cursor: pointer;
}

.active {
  background: url('@/assets/img/dyCyl/btn-active2.png') no-repeat;
  background-size: 100% 100%;
}

.ccWrap {
  display: flex;
  align-items: center;
}

.wOne {
  /* background-color: #a0e3ff; */
  width: 560px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: inline-block;
  /*text-align: center; */
}

.tfam {
  font-family: YouSheBiaoTiHei;
  /* font-weight: bolder; */
}

.newT {
  background: linear-gradient(180deg, #ffc460 0%, #fd852e 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

/********************************** 大挪移页面 **********************************/

.screenWrap {
  width: 100%;
  height: 100%;
  /* background-color: #BAD3FF; */
  position: relative;
}

.positionClass {
  top: -106px;
  left: 0px;
}

.testBg {
  width: 2799px;
  height: 1557px;
  background-color: #bad3ff;
  background: url('@/assets/img/dyCyl/zsdt.png') no-repeat;
  background-size: 100% 100%;
  /* position: absolute;
  top: 0px;
  left: 0px; */
}

.count {
  width: 1460px;
  height: 220px;
  background: url('@/assets/img/dyCyl/count_bg.png') no-repeat 0px -148px;
  position: absolute;
  /* top: 240px; */
  top: 50px;
  left: 50px;
  /* margin:120px 0 0 554px; */
  display: flex;
  justify-content: space-evenly;
  font-size: 40px;
  color: #fff;
  font-weight: 500;
  text-align: center;
  line-height: 80px;
  padding: 25px 0;
  box-sizing: border-box;
}

.col_b {
  background: linear-gradient(180deg, #ffffff 0%, #a0e3ff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  font-weight: 700;
  font-size: 65px;
  /* font-family: 'DIN-Bold'; */
}

.mapnuo {
  position: absolute;

  /* width: 1700px;
  height: 1350px;
  top: 610px;
  left: 438px; */

  width: calc(1700px * 0.8);
  height: calc(1350px * 0.8);
  /* width: calc(1400px * 0.88);
  height: calc(1400px * 0.88); */
  top: 122px;
  left: 682px;
}

.map-bgnuo {
  position: absolute;
  background: url('@/assets/img/dyCyl/map_bg.png') no-repeat;
  background-size: 100% 100%;

  /* width: 1886px;
  height: 945px;
  top: 915px;
  left: 355px; */

  width: calc(1886px * 0.9);
  height: calc(945px * 0.9);
  top: 605px;
  left: 537px;
}

.mapDesClass {
  width: 2755px;
  height: 1462px;
  position: absolute;
  top: 0px;
  left: 0px;
  background: url('@/assets/img/dyCyl/mapDescs.png') no-repeat;
  background-size: 100% 100%;
}

.box {
  width: calc(1188px * 0.8);
  height: 1150px;
  background: url('@/assets/img/dyCyl/box_bg.png') no-repeat;
  background-size: 100% 100%;
  position: absolute;
  right: 0px;
  /* top: 180px; */
  top: 0px;
}

.box1 {
  /* top: 1050px; */
  /* top: 1145px; */
  top: 600px;
}

.box_top {
  width: 100%;
  height: 90px;
  background: url('@/assets/img/dyCyl/box_top_bg.png') no-repeat;
  background-size: 100% 100%;
}

.box_top > span {
  font-size: 48px;
  line-height: 90px;
  margin-left: 88px;
  font-weight: 700;
  background: linear-gradient(180deg, #ffffff 0%, #dcefff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.wNew {
  width: 66% !important;
}

.titleWrap {
  height: 150px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.title_l {
  display: flex;
  align-items: center;
}

.titleImg {
  width: 60px;
  height: 60px;
  margin-left: 48px;
  cursor: pointer;
  margin-top: 9px;
}

/* 二级菜单样式 */
.el-popover {
  background-color: rgba(5, 36, 73, 1);
  width: 1688px;
  height: 600px;
  border: 3px solid rgba(5, 145, 243, 1);
  border-radius: 15px;
  padding: 60px 60px 60px 60px;
}

.contentClass {
  width: 100%;
  height: 100%;
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-template-rows: repeat(4, 1fr);
}

.gridInner {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
}

.newChartWrap {
  width: 100%;
  height: 100%;
  position: relative;
}

.chartNewClass {
  width: 100%;
  height: 100%;
  /* background-color: rgba(255, 196, 96, .3); */
  /* cursor: pointer; */
}

.zoomBig {
  position: absolute;
  right: 332px;
  bottom: 40px;
  width: 80px;
  height: 80px;
  background: url('@/assets/img/dyCyl/big.png') no-repeat;
  background-size: 100% 100%;
  cursor: pointer;
}

.zoomSmall {
  position: absolute;
  right: 168px;
  bottom: 40px;
  width: 80px;
  height: 80px;
  background: url('@/assets/img/dyCyl/small.png') no-repeat;
  background-size: 100% 100%;
  cursor: pointer;
}

/****************************************************************** 图谱样式 ******************************************************************/

.nodeWrap {
  position: relative;
}

.txtClass {
  background-color: #395372;
  white-space: nowrap;
  border: 3px solid #bad3ff;
  padding: 8px 20px 8px 20px;
  font-size: 36px;
  border-radius: 10px;
}

.dotClass {
  position: absolute;
  right: 0;
  top: 50%;
  padding: 8px;
  border-radius: 50%;
  background: #04447a;
  border: 1px solid #0091ff;
  font-size: 22px;
  color: #ffffff;
  font-weight: bolder;
  width: 34px;
  height: 34px;
  transform: translate(72%, -50%);
  display: flex;
  align-items: center;
  justify-content: center;
}

.htabClass {
  display: flex;
  align-items: center;
}

.countClass {
  display: flex;
  align-items: center;
  margin-left: 8px;
}

.unitCount {
  margin-left: 8px;
}

.tooltipClass {
  font-size: 36px;
  background-color: #041b34;
  border-radius: 10px;
  color: '#fff';
  padding: 20px 300px 200px 30px;
  /* display: flex;
  justify-content: center;
  align-items: center; */
}
</style>
