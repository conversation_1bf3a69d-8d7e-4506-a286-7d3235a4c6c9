<template>
  <div class="qyhxContainer">
    <div class="inneLeft">
      <qyhxLeft
        v-if="allMessage && allMessage.length > 0 && allMessage[0].tyshxydm"
        :allMessage="allMessage"
        :qyhxGsbq="qyhxGsbq"
        :qyhxJbxxList="qyhxJbxxList"
      ></qyhxLeft>
    </div>
    <div class="inneCenter">
      <qyhxCenter
        v-if="allMessage && allMessage.length > 0 && allMessage[0].tyshxydm"
        :allMessage="allMessage"
        @qyhxGsbq="handleQyhxGsbq"
        @qyhxJbxxList="handleQyhxJbxxList"
      ></qyhxCenter>
    </div>
    <div class="innerRight">
      <qyhxRight
        v-if="allMessage && allMessage.length > 0 && allMessage[0].tyshxydm"
        :allMessage="allMessage"
      ></qyhxRight>
    </div>
    <!-- 左右的刻度背景 -->
    <div class="back-btn" @click="close"></div>
  </div>
</template>
<script>
import $ from 'jquery'
import axios from 'axios'
import { getCsdnInterface2 } from '@/api/csdnIndexApi'
import qyhxLeft from '@/pages/qyzf/qyhx/qyhxLeft.vue'
import qyhxRight from '@/pages/qyzf/qyhx/qyhxRight.vue'
import qyhxCenter from '@/pages/qyzf/qyhx/qyhxCenter.vue'
export default {
  props: {},
  components: { qyhxLeft, qyhxRight, qyhxCenter },
  data() {
    return {
      winTop: '40px',
      qyhxGsbq: '',
      qyhxJbxxList: '',
      allMessage: [
        {
          qymc: '',
          tyshxydm: '',
        },
      ],
    }
  },
  watch: {},
  mounted() {
    // 解析从路由传递过来的数组参数
    try {
      const queryAllMessage = this.$route.query.allMessage
      if (queryAllMessage) {
        this.allMessage = JSON.parse(queryAllMessage)
      }
    } catch (error) {
      console.error('解析allMessage参数失败:', error)
      // 如果解析失败，保持默认值
    }
    console.log('this.allMessage', this.allMessage)

    // 确保allMessage是数组且有数据
    if (Array.isArray(this.allMessage) && this.allMessage.length > 0) {
      this.$EventBus.$emit('changeTopTitle', this.allMessage[0].qymc)
    }
  },
  methods: {
    handleQyhxGsbq(value) {
      this.qyhxGsbq = value
    },
    handleQyhxJbxxList(value) {
      this.qyhxJbxxList = value
    },
    close() {
      this.$router.go(-1)
      this.$EventBus.$emit('changeTopTitle', '工业智服')
    },
  },
}
</script>

<style lang="less" scoped>
.qyhxContainer {
  position: relative;
  width: 100%;
  height: 100%;
  z-index: 2;
  .inneLeft {
    position: absolute;
    top: 0;
    z-index: 2;
    display: flex;
    justify-content: space-between;
  }
  .innerRight {
    position: absolute;
    top: 0;
    right: 0;
    z-index: 2;
    display: flex;
    justify-content: space-between;
  }
  .inneCenter {
    position: absolute;
    left: 1660px;
    top: 0;
    z-index: 2;
  }
}
.back-btn {
  width: 120px;
  height: 120px;
  position: absolute;
  top: 220px;
  right: 60px;
  background-image: url('@/pages/qyzf/img/qyhxCenter/back.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
  z-index: 11910;
  cursor: pointer;
}
</style>
