<template>
  <div class="fxyjfxLeft">
    <div class="left1">
      <MainTitle title="重点区域预警">
        <template #right>
          <el-select v-model="selectedOption" @change="changeEchartHour" placeholder="请选择区域">
            <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </template>
      </MainTitle>
      <SubTitle title="24小时人流变化情况" />
      <div id="rlbh_hour" class="rlbh_hour"></div>
      <SubTitle title="近30天人流变化情况" />
      <div id="rlbh_day" class="rlbh_day"></div>
      <SubTitle title="重点区域预警" />
      <div id="zdqy_yj" class="zdqy_yj">
        <CommonTable
          :height="'600px'"
          :tableData="tableData"
          style="margin-top: 16px"
          :row-click="rowClick"
        ></CommonTable>
      </div>
    </div>
    <div class="left2">
      <MainTitle title="网格精细预警" />
      <SubTitle title="24小时人流变化情况" />
      <div id="rlbh_hour1" class="rlbh_hour1"></div>
      <SubTitle title="近30天人流变化情况" />
      <div id="rlbh_day1" class="rlbh_day1"></div>
      <SubTitle title="网格预警" />
      <div id="wg_yj" class="wg_yj">
        <CommonTable
          :height="'600px'"
          :tableData="tableData1"
          style="margin-top: 16px"
          :row-click="rowClick"
        ></CommonTable>
      </div>
    </div>

    <!-- 预警详情弹框 -->
    <WarningDetailDialog :visible="showWarningDialog" :warningData="selectedWarningData" @close="closeWarningDialog" />
    <Commondialog :title="'流转详情'" :dialogFlag="visible" @close="visible = false" :dialog-width="'1800px'">
      <WarningChange :msgObj="msgObj" @close="visible = false" />
    </Commondialog>
  </div>
</template>

<script>
import MainTitle from '@/components/MainTitle.vue'
import SubTitle from '@/components/SubTitle.vue'
import CommonTable from '@/components/CommonTable'
import WarningDetailDialog from './WarningDetailDialog.vue'
import WarningChange from './WarningChange.vue'
import { getCsdnInterface1 } from '@/api/csdnIndexApi'
import Commondialog from '@/components/Commondialog'
export default {
  name: 'fxyjfxLeft',
  components: { MainTitle, SubTitle, CommonTable, WarningDetailDialog, Commondialog, WarningChange },
  data() {
    return {
      charts: [], // 存储图表实例
      showWarningDialog: false, // 控制弹框显示
      selectedWarningData: {}, // 选中的预警数据
      selectedOption: '白兆山李白文化风景区', // 默认选中的区域
      options: [
        { value: '白兆山李白文化风景区', label: '白兆山李白文化风景区' },
        { value: '钱冲古银杏国家森林公园', label: '钱冲古银杏国家森林公园' },
        { value: '安陆市东大时代广场', label: '安陆市东大时代广场' },
        { value: '安陆市恒泰购物广场', label: '安陆市恒泰购物广场' },
      ],
      // 重点区域预警表格数据
      tableData: {
        thead: [
          { label: '区域', property: 'area_name', width: 90, align: 'left' },
          { label: '人数', property: 'people_count', width: 80, align: 'left' },
          { label: '超值%', property: 'exceed_percentage', width: 100, align: 'left' },
          { label: '级别', property: 'warning_level', width: 80, align: 'left' },
          { label: '状态', property: 'status', width: 80, align: 'left' },
          { label: '预警时间', property: 'warning_time', width: 120, align: 'left' },
        ],
        tbody: [],
      },
      // 网格预警表格数据
      tableData1: {
        thead: [
          { label: '区域', property: 'area_name', width: 90, align: 'left' },
          { label: '人数', property: 'people_count', width: 80, align: 'left' },
          { label: '超值%', property: 'exceed_percentage', width: 100, align: 'left' },
          { label: '级别', property: 'warning_level', width: 80, align: 'left' },
          { label: '状态', property: 'status', width: 80, align: 'left' },
          { label: '预警时间', property: 'warning_time', width: 120, align: 'left' },
        ],
        tbody: [],
      },
      msgObj: {},
      visible: false,
    }
  },

  mounted() {
    this.$nextTick(() => {
      this.initApi()
    })
  },

  beforeDestroy() {
    // 销毁所有图表实例，防止内存泄漏
    this.charts.forEach((chart) => {
      if (chart && !chart.isDisposed()) {
        chart.dispose()
      }
    })
    this.charts = []
  },

  methods: {
    rowClick(obj) {
      console.log('点击的行数据:', obj)

      if (obj.status == '待确认') {
        this.msgObj = obj
        this.visible = true
      } else {
        // 设置选中的预警数据
        this.selectedWarningData = {
          id: obj.id,
          type: obj.mc,
          description: obj.ms,
          time: obj.warning_time,
          status: obj.status,
        }
        // 显示弹框
        this.showWarningDialog = true
      }
    },

    closeWarningDialog() {
      this.showWarningDialog = false
      this.selectedWarningData = {}
    },

    // 处理区域选择变化
    changeEchartHour(value) {
      console.log('区域选择变化:', value)
      // 根据选择的区域重新获取数据
      this.getData()
    },

    initApi() {
      this.getData()
    },
    getData() {
      getCsdnInterface1('qyhx_pop_rkbhqk', {
        name: this.selectedOption, // 传递选中的区域名称
      }).then((res) => {
        let resdata = res.data.data
        this.initRlbhHourChart(resdata)
      })
      getCsdnInterface1('rkhl_test', { type: '网格精细预警24' }).then((res) => {
        let resdata = res.data.data
        this.initRlbhHour1Chart(resdata)
      })
      getCsdnInterface1('qyhx_pop_30rlbh', { name: this.selectedOption }).then((res) => {
        let resdata = res.data.data
        this.initRlbhDayChart(resdata)
      })
      getCsdnInterface1('rkhl_test', { type: '网格精细预警30' }).then((res) => {
        let resdata = res.data.data
        this.initRlbhDay1Chart(resdata)
      })
      getCsdnInterface1('rkhl_zdqy_wg_yj', { warningType: '重点区域预警' }).then((res) => {
        let resdata = res.data.data
        this.tableData.tbody = resdata
      })
      getCsdnInterface1('rkhl_zdqy_wg_yj', { warningType: '网格预警' }).then((res) => {
        let resdata = res.data.data
        this.tableData1.tbody = resdata
      })
    },

    // 处理窗口大小变化
    handleResize() {
      this.charts.forEach((chart) => {
        if (chart && !chart.isDisposed()) {
          chart.resize()
        }
      })
    },

    // 重点区域预警 - 24小时人流变化情况
    initRlbhHourChart(resdata) {
      // 销毁已存在的图表实例
      const chartDom = document.getElementById('rlbh_hour')
      if (chartDom) {
        this.$echarts.dispose(chartDom)
      }
      const chart = this.$echarts.init(chartDom)
      this.charts.push(chart)

      const option = {
        tooltip: {
          trigger: 'axis',
          backgroundColor: 'rgba(0, 0, 0, 0.8)',
          borderColor: '#00c0ff',
          borderWidth: 1,
          textStyle: {
            color: '#fff',
            fontSize: 32,
          },
          formatter: function (params) {
            return `${params[0].axisValue}<br/>人数: ${params[0].value}人`
          },
        },
        grid: {
          left: '8%',
          right: '5%',
          top: '15%',
          bottom: '15%',
          containLabel: true,
        },
        xAxis: {
          type: 'category',
          data: resdata.map((item) => item.time_label),
          axisLabel: {
            color: '#fff',
            fontSize: 32,
            interval: 3, // 每4个小时显示一次
          },
          axisLine: {
            lineStyle: {
              color: 'rgba(255, 255, 255, 0.3)',
            },
          },
          axisTick: {
            show: false,
          },
        },
        yAxis: {
          type: 'value',
          axisLabel: {
            color: '#fff',
            fontSize: 32,
            formatter: '{value}',
          },
          axisLine: {
            lineStyle: {
              color: 'rgba(255, 255, 255, 0.3)',
            },
          },
          splitLine: {
            lineStyle: {
              color: 'rgba(255, 255, 255, 0.1)',
            },
          },
        },
        series: [
          {
            type: 'line',
            data: resdata.map((item) => item.hourly_count),
            smooth: true,
            symbol: 'none',
            lineStyle: {
              color: '#00c0ff',
              width: 2,
            },
            areaStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: 'rgba(0, 192, 255, 0.6)',
                  },
                  {
                    offset: 1,
                    color: 'rgba(0, 192, 255, 0.1)',
                  },
                ],
              },
            },
          },
        ],
      }

      chart.setOption(option)
    },

    // 重点区域预警 - 近30天人流变化情况
    initRlbhDayChart(resdata) {
      // 销毁已存在的图表实例
      const chartDom = document.getElementById('rlbh_day')
      if (chartDom) {
        this.$echarts.dispose(chartDom)
      }
      const chart = this.$echarts.init(chartDom)
      this.charts.push(chart)

      const option = {
        tooltip: {
          trigger: 'axis',
          backgroundColor: 'rgba(0, 0, 0, 0.8)',
          borderColor: '#00c0ff',
          borderWidth: 1,
          textStyle: {
            color: '#fff',
            fontSize: 32,
          },
          formatter: function (params) {
            return `${params[0].axisValue}<br/>人数: ${params[0].value}人`
          },
        },
        grid: {
          left: '8%',
          right: '5%',
          top: '15%',
          bottom: '15%',
          containLabel: true,
        },
        xAxis: {
          type: 'category',
          data: resdata.map((item) => item.date_label),
          axisLabel: {
            color: '#fff',
            fontSize: 32,
            interval: 0, // 显示所有日期标签
            // rotate: 45
          },
          axisLine: {
            lineStyle: {
              color: 'rgba(255, 255, 255, 0.3)',
            },
          },
          axisTick: {
            show: false,
          },
        },
        yAxis: {
          type: 'value',
          axisLabel: {
            color: '#fff',
            fontSize: 32,
            formatter: '{value}',
          },
          axisLine: {
            lineStyle: {
              color: 'rgba(255, 255, 255, 0.3)',
            },
          },
          splitLine: {
            lineStyle: {
              color: 'rgba(255, 255, 255, 0.1)',
            },
          },
        },
        series: [
          {
            type: 'line',
            data: resdata.map((item) => item.avg_peak_count),
            smooth: true,
            symbol: 'none',
            lineStyle: {
              color: '#00c0ff',
              width: 2,
            },
            areaStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: 'rgba(0, 192, 255, 0.6)',
                  },
                  {
                    offset: 1,
                    color: 'rgba(0, 192, 255, 0.1)',
                  },
                ],
              },
            },
          },
        ],
      }

      chart.setOption(option)
    },

    // 网格精细预警 - 24小时人流变化情况
    initRlbhHour1Chart(resdata) {
      // 销毁已存在的图表实例
      const chartDom = document.getElementById('rlbh_hour1')
      if (chartDom) {
        this.$echarts.dispose(chartDom)
      }
      const chart = this.$echarts.init(chartDom)
      this.charts.push(chart)

      const option = {
        tooltip: {
          trigger: 'axis',
          backgroundColor: 'rgba(0, 0, 0, 0.8)',
          borderColor: '#ffa500',
          borderWidth: 1,
          textStyle: {
            color: '#fff',
            fontSize: 32,
          },
          formatter: function (params) {
            return `${params[0].axisValue}:00<br/>人数: ${params[0].value}人`
          },
        },
        grid: {
          left: '8%',
          right: '5%',
          top: '15%',
          bottom: '15%',
          containLabel: true,
        },
        xAxis: {
          type: 'category',
          data: resdata.map((item) => item.name),
          axisLabel: {
            color: '#fff',
            fontSize: 32,
            interval: 3, // 每4个小时显示一次
          },
          axisLine: {
            lineStyle: {
              color: 'rgba(255, 255, 255, 0.3)',
            },
          },
          axisTick: {
            show: false,
          },
        },
        yAxis: {
          type: 'value',
          axisLabel: {
            color: '#fff',
            fontSize: 32,
            formatter: '{value}',
          },
          axisLine: {
            lineStyle: {
              color: 'rgba(255, 255, 255, 0.3)',
            },
          },
          splitLine: {
            lineStyle: {
              color: 'rgba(255, 255, 255, 0.1)',
            },
          },
        },
        series: [
          {
            type: 'line',
            data: resdata.map((item) => item.value),
            smooth: true,
            symbol: 'none',
            lineStyle: {
              color: '#ffa500',
              width: 2,
            },
            areaStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: 'rgba(255, 165, 0, 0.6)',
                  },
                  {
                    offset: 1,
                    color: 'rgba(255, 165, 0, 0.1)',
                  },
                ],
              },
            },
          },
        ],
      }

      chart.setOption(option)
    },

    // 网格精细预警 - 近30天人流变化情况
    initRlbhDay1Chart(resdata) {
      // 销毁已存在的图表实例
      const chartDom = document.getElementById('rlbh_day1')
      if (chartDom) {
        this.$echarts.dispose(chartDom)
      }
      const chart = this.$echarts.init(chartDom)
      this.charts.push(chart)

      const option = {
        tooltip: {
          trigger: 'axis',
          backgroundColor: 'rgba(0, 0, 0, 0.8)',
          borderColor: '#ffa500',
          borderWidth: 1,
          textStyle: {
            color: '#fff',
            fontSize: 32,
          },
          formatter: function (params) {
            return `${params[0].axisValue}<br/>人数: ${params[0].value}人`
          },
        },
        grid: {
          left: '8%',
          right: '5%',
          top: '15%',
          bottom: '15%',
          containLabel: true,
        },
        xAxis: {
          type: 'category',
          data: resdata.map((item) => item.name),
          axisLabel: {
            color: '#fff',
            fontSize: 32,
            interval: 4, // 每5天显示一次
            // rotate: 45
          },
          axisLine: {
            lineStyle: {
              color: 'rgba(255, 255, 255, 0.3)',
            },
          },
          axisTick: {
            show: false,
          },
        },
        yAxis: {
          type: 'value',
          axisLabel: {
            color: '#fff',
            fontSize: 32,
            formatter: '{value}',
          },
          axisLine: {
            lineStyle: {
              color: 'rgba(255, 255, 255, 0.3)',
            },
          },
          splitLine: {
            lineStyle: {
              color: 'rgba(255, 255, 255, 0.1)',
            },
          },
        },
        series: [
          {
            type: 'line',
            data: resdata.map((item) => item.value),
            smooth: true,
            symbol: 'none',
            lineStyle: {
              color: '#ffa500',
              width: 2,
            },
            areaStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: 'rgba(255, 165, 0, 0.6)',
                  },
                  {
                    offset: 1,
                    color: 'rgba(255, 165, 0, 0.1)',
                  },
                ],
              },
            },
          },
        ],
      }

      chart.setOption(option)
    },
  },
}
</script>

<style scoped lang="less">
.fxyjfxLeft {
  width: 1550px;
  display: flex;
  margin-left: 50px;
  .left1 {
    width: 760px;
    .rlbh_hour {
      width: 100%;
      height: 464px;
    }
    .rlbh_day {
      width: 100%;
      height: 464px;
    }
    .zdqy_yj {
      width: 100%;
      height: 610px;
    }
  }
  .left2 {
    width: 760px;
    margin-left: 30px;
    .rlbh_hour1 {
      width: 100%;
      height: 464px;
    }
    .rlbh_day1 {
      width: 100%;
      height: 464px;
    }
    .wg_yj {
      width: 100%;
      height: 610px;
    }
  }
}
</style>

<style lang="less">
// Element UI 下拉选择框弹出层样式
.el-select-dropdown {
  background: rgba(0, 30, 60, 0.95) !important;
  border: 1px solid rgba(0, 192, 255, 0.3) !important;

  .el-select-dropdown__item {
    color: #fff !important;

    &:hover {
      background: rgba(0, 192, 255, 0.2) !important;
    }

    &.selected {
      background: rgba(0, 192, 255, 0.3) !important;
      color: #00c0ff !important;
    }
  }

  .el-scrollbar__view {
    .el-select-dropdown__item {
      color: #fff !important;
    }
  }
}

.el-popper[x-placement^='bottom'] .el-popper__arrow {
  border-bottom-color: rgba(0, 192, 255, 0.3) !important;
}

.el-popper[x-placement^='top'] .el-popper__arrow {
  border-top-color: rgba(0, 192, 255, 0.3) !important;
}
</style>
