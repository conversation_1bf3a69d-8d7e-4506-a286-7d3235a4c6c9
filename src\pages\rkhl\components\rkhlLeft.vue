<template>
  <div class="rkhl-left">
    <!-- 总体人口情况 -->
    <div class="section area-monitor">
      <MainTitle title="总体人口情况" size="large" />
      <div class="section-content">
        <div class="row">
          <PopulationTrend />
          <PersonnelRanking />
          <PopulationStructure />
        </div>
      </div>
    </div>
    <!-- 实时人口概况 -->
    <div class="section area-monitor">
      <MainTitle title="实时人口概况" size="large" />
      <div class="section-content">
        <div class="row">
          <PopulationChangeChart />
          <HourlyTrend />
          <!-- <PopulationFlow @open-slsw="handleOpenSlsw" /> -->
        </div>
      </div>
    </div>
    <!-- 重点区域实时人口情况 -->
    <div class="section area-monitor">
      <MainTitle title="重点区域实时人口情况" size="large">
        <template #right>
          <div class="area-selector">
            <el-select v-model="selectedArea" @change="handleAreaChange" placeholder="请选择区域">
              <el-option
                v-for="item in areaOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </div>
        </template>
      </MainTitle>
      <div class="section-content">
        <div class="row">
          <GovernmentFlow :selected-area="selectedArea" />
          <AreaPopulationChange :selected-area="selectedArea" />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import MainTitle from '@/components/MainTitle.vue'
import PersonnelRanking from './PersonnelRanking.vue'
import HourlyTrend from './HourlyTrend.vue'
import PopulationChangeChart from './PopulationChangeChart.vue'
import PopulationStructure from './PopulationStructure.vue'
import PopulationTrend from './PopulationTrend.vue'
import PopulationFlow from './PopulationFlow.vue'
import GovernmentFlow from './GovernmentFlow.vue'
import AreaPopulationChange from './AreaPopulationChange.vue'
import PopulationPortrait from './PopulationPortrait.vue'
import { getCsdnInterface1 } from '@/api/csdnIndexApi'

export default {
  name: 'RkhlLeft',
  components: {
    MainTitle,
    PersonnelRanking,
    HourlyTrend,
    PopulationChangeChart,
    PopulationStructure,
    PopulationTrend,
    PopulationFlow,
    GovernmentFlow,
    AreaPopulationChange,
    PopulationPortrait,
  },
  data() {
    return {
      selectedArea: '白兆山李白文化风景区', // 默认选中的区域
      areaOptions: [
        { label: '白兆山李白文化风景区', value: '白兆山李白文化风景区' },
        { label: '钱冲古银杏国家森林公园', value: '钱冲古银杏国家森林公园' },
        { label: '安陆市东大时代广场', value: '安陆市东大时代广场' },
        { label: '安陆市恒泰购物广场', value: '安陆市恒泰购物广场' },
      ],
    }
  },
  mounted() {},
  methods: {
    // 处理区域选择变化
    handleAreaChange(value) {
      console.log('区域选择变化:', value)
      // selectedArea 已经通过 v-model 自动更新
      // 子组件会通过 watch 监听 selectedArea prop 的变化并自动重新请求数据
    },

    // 处理打开slsw弹窗事件
    handleOpenSlsw(payload) {
      this.$emit('open-slsw', payload)
    },
  },
}
</script>

<style lang="less" scoped>
* {
  margin: 0;
  padding: 0;
}

.rkhl-left {
  width: 1550px;
  height: 1862px;
  padding: 10px 0 0px 0;
  margin-left: 50px;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;

  .section {
    width: 100%;
    margin-bottom: 40px;

    &.population-monitor {
      height: 66.67%; /* 2/3 的高度 */
    }

    &.area-monitor {
      height: 33.33%; /* 1/3 的高度 */
    }

    .section-content {
      width: 100%;
      height: calc(100% - 110px);
      position: relative;

      .row {
        display: flex;
        gap: 20px;
        margin-bottom: 20px;
        height: calc(50% - 10px);

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }

  .area-selector {
    /deep/ .el-select {
      width: 280px;

      .el-input__inner {
        cursor: pointer;
        height: 40px;
        border-radius: 4px;
        color: #fff;
        background-color: rgba(19, 44, 78, 0.8);
        border: 1px solid #359cf8;
        font-size: 18px;
        padding: 0 15px;
      }

      .el-input__suffix {
        .el-select__caret {
          color: #359cf8;
        }
      }
    }

    /deep/ .el-select-dropdown {
      background-color: rgba(19, 44, 78, 0.95);
      border: 1px solid #359cf8;

      .el-select-dropdown__item {
        color: #d2d3d4;
        font-size: 16px;

        &.selected {
          color: #359cf8;
          font-weight: bold;
        }

        &.hover,
        &:hover {
          background-color: rgba(53, 156, 248, 0.2);
          color: #fff;
        }
      }
    }
  }
}
</style>
