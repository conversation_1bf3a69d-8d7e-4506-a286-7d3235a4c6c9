<template>
  <el-dialog
    title=""
    :top="winTop"
    ref="policyDialog"
    custom-class="custom-class-dialog"
    :show-close="false"
    :modal="true"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :visible.sync="dialogVisible"
    width="2209px"
    :modal-append-to-body="true"
    :append-to-body="true"
    @open="dialogOpen"
  >
    <div class="dialog-wrapper">
      <!-- 标题栏 -->
      <div class="dialog-header">
        <div class="header-title">政策详情</div>
        <i class="el-icon-close close-btn" @click="closeDialog"></i>
      </div>

      <!-- 内容区域 -->
      <div class="dialog-content">
        <!-- 基本信息 -->
        <div class="section">
          <div class="section-title">
            <div class="title-icon"></div>
            <span>基本信息</span>
          </div>
          <div class="info-table">
            <div class="info-row">
              <div class="info-cell label">事项名称</div>
              <div class="info-cell value">{{ detailData.item_name }}</div>
              <div class="info-cell label">责任部门</div>
              <div class="info-cell value">{{ detailData.duty_dept }}</div>
            </div>
            <div class="info-row">
              <div class="info-cell label">办理方式</div>
              <div class="info-cell value">{{ detailData.handle_way }}</div>
              <div class="info-cell label">适用时间</div>
              <div class="info-cell value">{{ detailData.applicable_date }}</div>
            </div>
            <div class="info-row">
              <div class="info-cell label">适用对象</div>
              <div class="info-cell value full-width">{{ detailData.applicable_subject }}</div>
            </div>
            <div class="info-row">
              <div class="info-cell label">事项依据</div>
              <div class="info-cell value full-width">{{ detailData.item_basis }}</div>
            </div>
            <div class="info-row">
              <div class="info-cell label">事项内容</div>
              <div class="info-cell value full-width">{{ detailData.item_content }}</div>
            </div>
          </div>
        </div>

        <!-- 企业匹配情况 -->
        <!-- <div class="section">
          <div class="section-title">
            <div class="title-icon"></div>
            <span>企业匹配情况</span>
          </div>
          <div class="match-charts">
            <div class="chart-item">
              <div class="chart-title">政策匹配企业</div>
              <div id="policyMatchChart" class="chart-container"></div>
            </div>
            <div class="chart-item">
              <div class="chart-title">匹配企业规模分布</div>
              <div id="enterpriseScaleChart" class="chart-container"></div>
            </div>
          </div>
        </div> -->

        <!-- 政策兑付情况 -->
        <!-- <div class="section">
          <div class="section-title">
            <div class="title-icon"></div>
            <span>政策兑付情况</span>
          </div>
          <div class="payment-stats">
            <div class="stat-card large">
              <div class="stat-label">兑付企业数</div>
              <div class="stat-value">
                <count-to
                  :start-val="0"
                  :end-val="paymentData.enterprise_count || 6323"
                  :duration="2000"
                  :decimals="0"
                  separator=","
                ></count-to>
                <span class="stat-unit">家</span>
              </div>
            </div>
            <div class="stat-card large">
              <div class="stat-label">兑付金额</div>
              <div class="stat-value">
                <count-to
                  :start-val="0"
                  :end-val="paymentData.total_amount || 47110286"
                  :duration="2000"
                  :decimals="2"
                  separator=","
                ></count-to>
                <span class="stat-unit">元</span>
              </div>
            </div>
          </div>
          <div class="payment-chart-wrapper">
            <div class="chart-title">企业兑付分析</div>
            <div id="paymentAnalysisChart" class="payment-chart-container"></div>
          </div>
        </div> -->
      </div>
    </div>
  </el-dialog>
</template>

<script>
import $ from 'jquery'

export default {
  name: 'PolicyDetailDialog',
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    detailData: {
      type: Object,
      default: () => ({
        item_name: '',
        item_basis: '',
        item_content: '',
        applicable_subject: '',
        applicable_date: '',
        duty_dept: '',
        handle_way: '',
      }),
    },
    matchData: {
      type: Object,
      default: () => ({
        total_count: 0,
        applied_count: 0,
        not_applied_count: 0,
        apply_rate: '0%',
        enterprise_list: [],
      }),
    },
    paymentData: {
      type: Object,
      default: () => ({
        total_amount: 0,
        enterprise_count: 0,
        payment_count: 0,
        avg_amount: 0,
        payment_list: [],
      }),
    },
  },
  data() {
    return {
      dialogVisible: false,
      winTop: '40px',
    }
  },
  watch: {
    visible(val) {
      this.dialogVisible = val
      if (val) {
        this.$nextTick(() => {
          setTimeout(() => {
            this.winTop =
              (document.documentElement.clientHeight - $(this.$refs.policyDialog.$el).find('.el-dialog').height()) / 2 +
              'px'
            // 初始化图表
            this.initCharts()
          }, 100)
        })
      }
    },
    matchData: {
      handler() {
        if (this.dialogVisible) {
          this.$nextTick(() => {
            this.initCharts()
          })
        }
      },
      deep: true,
    },
    paymentData: {
      handler() {
        if (this.dialogVisible) {
          this.$nextTick(() => {
            this.initPaymentAnalysisChart()
          })
        }
      },
      deep: true,
    },
  },
  methods: {
    closeDialog() {
      this.$emit('update:visible', false)
      this.$emit('close')
    },
    dialogOpen() {
      this.$emit('open')
    },
    getMatchColor(score) {
      if (score >= 80) return '#67C23A'
      if (score >= 60) return '#E6A23C'
      return '#F56C6C'
    },
    initCharts() {
      this.initPolicyMatchChart()
      this.initEnterpriseScaleChart()
      this.initPaymentAnalysisChart()
    },
    // 政策匹配企业饼图
    initPolicyMatchChart() {
      const chartDom = document.getElementById('policyMatchChart')
      if (!chartDom) return

      this.$echarts.dispose(chartDom)
      const chart = this.$echarts.init(chartDom)

      const matchedCount = this.matchData.total_count || 6451
      const notMatchedCount = 561

      const data = [
        { name: '匹配企业', value: matchedCount },
        { name: '未匹配企业', value: notMatchedCount },
      ]

      const total = data.reduce((sum, d) => sum + d.value, 0)

      const option = {
        color: ['#1E90FF', '#FFA500'],
        tooltip: {
          trigger: 'item',
          backgroundColor: 'rgba(0, 0, 0, 0.8)',
          borderColor: '#23daff',
          textStyle: {
            color: '#fff',
            fontSize: 28,
          },
          formatter: function (params) {
            return params.name + ': ' + params.value + '家 (' + params.percent + '%)'
          },
        },
        legend: {
          orient: 'vertical',
          right: '8%',
          top: 'center',
          icon: 'circle',
          itemWidth: 24,
          itemHeight: 24,
          itemGap: 30,
          textStyle: {
            color: '#fff',
            fontSize: 32,
          },
          formatter: function (name) {
            const item = data.find((d) => d.name === name)
            const percent = total > 0 ? Math.round((item.value / total) * 100) : 0
            return name + '  ' + item.value + '家  ' + percent + '%'
          },
        },
        graphic: [
          {
            type: 'image',
            id: 'centerIcon',
            left: 'center',
            top: 'center',
            z: 0,
            bounding: 'raw',
            style: {
              image: require('@/pages/qyzf/img/echarts-bg.png'),
              width: 180,
              height: 180,
            },
          },
        ],
        series: [
          {
            type: 'pie',
            radius: ['45%', '65%'],
            center: ['35%', '50%'],
            avoidLabelOverlap: false,
            itemStyle: {
              borderColor: '#002b37',
              borderWidth: 4,
            },
            label: {
              show: false,
            },
            emphasis: {
              label: {
                show: false,
              },
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)',
              },
            },
            data: data,
          },
        ],
      }

      chart.setOption(option)
    },
    // 匹配企业规模分布饼图
    initEnterpriseScaleChart() {
      const chartDom = document.getElementById('enterpriseScaleChart')
      if (!chartDom) return

      this.$echarts.dispose(chartDom)
      const chart = this.$echarts.init(chartDom)

      // 根据参考图片的数据
      const data = [
        { name: '微型企业', value: 2145 },
        { name: '小型企业', value: 1580 },
        { name: '中型企业', value: 1355 },
        { name: '大型企业', value: 564 },
      ]

      const total = data.reduce((sum, d) => sum + d.value, 0)

      const option = {
        color: ['#00D9FF', '#00FF9D', '#FFD700', '#FF6B6B'],
        tooltip: {
          trigger: 'item',
          backgroundColor: 'rgba(0, 0, 0, 0.8)',
          borderColor: '#23daff',
          textStyle: {
            color: '#fff',
            fontSize: 28,
          },
          formatter: function (params) {
            return params.name + ': ' + params.value + '家 (' + params.percent + '%)'
          },
        },
        legend: {
          orient: 'vertical',
          right: '8%',
          top: 'center',
          icon: 'circle',
          itemWidth: 24,
          itemHeight: 24,
          itemGap: 30,
          textStyle: {
            color: '#fff',
            fontSize: 32,
          },
          formatter: function (name) {
            const item = data.find((d) => d.name === name)
            const percent = total > 0 ? Math.round((item.value / total) * 100) : 0
            return name + '  ' + item.value + '家  ' + percent + '%'
          },
        },
        graphic: [
          {
            type: 'image',
            id: 'centerIcon',
            left: 'center',
            top: 'center',
            z: 0,
            bounding: 'raw',
            style: {
              image: require('@/pages/qyzf/img/echarts-bg.png'),
              width: 180,
              height: 180,
            },
          },
        ],
        series: [
          {
            type: 'pie',
            radius: ['45%', '65%'],
            center: ['35%', '50%'],
            avoidLabelOverlap: false,
            itemStyle: {
              borderColor: '#002b37',
              borderWidth: 4,
            },
            label: {
              show: false,
            },
            emphasis: {
              label: {
                show: false,
              },
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)',
              },
            },
            data: data,
          },
        ],
      }

      chart.setOption(option)
    },
    // 企业兑付分析图表(柱状图+折线图组合)
    initPaymentAnalysisChart() {
      const chartDom = document.getElementById('paymentAnalysisChart')
      if (!chartDom) return

      this.$echarts.dispose(chartDom)
      const chart = this.$echarts.init(chartDom)

      // 模拟数据 - 根据参考图片
      const months = ['202501', '202502', '202503', '202504', '202505', '202506', '202507', '202508', '202509']
      const enterpriseCount = [600, 650, 650, 450, 800, 450, 700, 650, 1100] // 兑付企业数
      const amount = [200, 500, 500, 400, 900, 100, 800, 300, 1100] // 兑付金额(万元)

      const option = {
        tooltip: {
          trigger: 'axis',
          backgroundColor: 'rgba(0, 0, 0, 0.8)',
          borderColor: '#23daff',
          textStyle: {
            color: '#fff',
            fontSize: 28,
          },
          axisPointer: {
            type: 'cross',
            crossStyle: {
              color: '#999',
            },
          },
          formatter: function (params) {
            let result = params[0].axisValue + '<br/>'
            params.forEach((item) => {
              const unit = item.seriesName === '兑付企业数' ? '家' : '万元'
              result += `${item.marker}${item.seriesName}: ${item.value}${unit}<br/>`
            })
            return result
          },
        },
        legend: {
          data: ['兑付企业数', '兑付金额'],
          top: '5%',
          right: '10%',
          textStyle: {
            color: '#fff',
            fontSize: 28,
          },
          itemWidth: 30,
          itemHeight: 20,
        },
        grid: {
          left: '8%',
          right: '8%',
          top: '20%',
          bottom: '15%',
          containLabel: true,
        },
        xAxis: {
          type: 'category',
          data: months,
          axisLabel: {
            color: '#fff',
            fontSize: 24,
            interval: 0,
          },
          axisLine: {
            lineStyle: {
              color: 'rgba(255, 255, 255, 0.3)',
            },
          },
          axisTick: {
            show: false,
          },
        },
        yAxis: [
          {
            type: 'value',
            name: '单位(家)',
            nameTextStyle: {
              color: '#fff',
              fontSize: 24,
            },
            axisLabel: {
              color: '#fff',
              fontSize: 24,
            },
            axisLine: {
              show: true,
              lineStyle: {
                color: 'rgba(255, 255, 255, 0.3)',
              },
            },
            splitLine: {
              lineStyle: {
                color: 'rgba(255, 255, 255, 0.1)',
              },
            },
          },
          {
            type: 'value',
            name: '单位(万元)',
            nameTextStyle: {
              color: '#fff',
              fontSize: 24,
            },
            axisLabel: {
              color: '#fff',
              fontSize: 24,
            },
            axisLine: {
              show: true,
              lineStyle: {
                color: 'rgba(255, 255, 255, 0.3)',
              },
            },
            splitLine: {
              show: false,
            },
          },
        ],
        series: [
          {
            name: '兑付企业数',
            type: 'bar',
            yAxisIndex: 0,
            data: enterpriseCount,
            barWidth: '30%',
            itemStyle: {
              color: '#1E90FF',
              borderRadius: [4, 4, 0, 0],
            },
          },
          {
            name: '兑付金额',
            type: 'line',
            yAxisIndex: 1,
            data: amount,
            smooth: true,
            symbol: 'circle',
            symbolSize: 8,
            itemStyle: {
              color: '#FFA500',
            },
            lineStyle: {
              width: 3,
              color: '#FFA500',
            },
          },
        ],
      }

      chart.setOption(option)
    },
  },
}
</script>

<style lang="less" scoped>
/deep/.el-dialog {
  background: #002b37;
  background-size: 100% 100%;
  border: 2px solid #23daff;
  border-radius: 0px;
  box-shadow: inset 0px 0px 55px 0px #21f0e2;

  .el-dialog__header {
    display: none;
  }

  .el-dialog__body {
    padding: 0;
  }
}

/deep/.el-table {
  background: transparent !important;

  &::before {
    display: none;
  }

  th,
  td {
    border-color: rgba(35, 218, 255, 0.3) !important;
  }

  .el-table__body tr:hover > td {
    background-color: rgba(35, 218, 255, 0.2) !important;
  }
}

.dialog-wrapper {
  width: 100%;
  height: 1800px;
  overflow: hidden;
}

.dialog-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 120px;
  padding: 0 40px;
  background: linear-gradient(90deg, rgba(35, 218, 255, 0.4) 0%, rgba(35, 218, 255, 0.1) 100%);

  .header-title {
    font-size: 56px;
    font-weight: bold;
    color: #ffffff;
  }

  .close-btn {
    font-size: 64px;
    color: #ffffff;
    cursor: pointer;
    transition: all 0.3s;

    &:hover {
      color: #23daff;
    }
  }
}

.dialog-content {
  padding: 40px;
  height: calc(1800px - 120px);
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 40px;

  &::-webkit-scrollbar {
    width: 8px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(13, 61, 77, 0.3);
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(35, 218, 255, 0.5);
    border-radius: 4px;

    &:hover {
      background: rgba(35, 218, 255, 0.7);
    }
  }
}

.section {
  .section-title {
    display: flex;
    align-items: center;
    margin-bottom: 30px;

    .title-icon {
      width: 54px;
      height: 54px;
      margin-right: 16px;
      background: url('~@/assets/qyzf/subtitle-icon.png') no-repeat center;
      background-size: contain;
    }

    span {
      font-size: 42px;
      font-weight: bold;
      color: #ffc460;
    }
  }
}

.info-table {
  background: rgba(13, 61, 77, 0.5);
  border: 1px solid rgba(35, 218, 255, 0.3);

  .info-row {
    display: flex;
    border-bottom: 1px solid rgba(35, 218, 255, 0.3);

    &:last-child {
      border-bottom: none;
    }

    .info-cell {
      padding: 32px 40px;
      font-size: 32px;
      border-right: 1px solid rgba(35, 218, 255, 0.3);

      &:last-child {
        border-right: none;
      }

      &.label {
        background: rgba(13, 61, 77, 0.3);
        color: #b8d8e8;
        min-width: 200px;
        flex-shrink: 0;
      }

      &.value {
        color: #ffffff;
        flex: 1;

        &.full-width {
          flex: 3;
        }
      }
    }
  }
}

.match-stats,
.payment-stats {
  display: flex;
  gap: 30px;
  margin-bottom: 30px;
}

.stat-card {
  flex: 1;
  background: rgba(13, 61, 77, 0.6);
  border: 1px solid rgba(35, 218, 255, 0.3);
  border-radius: 4px;
  padding: 30px 24px;
  text-align: center;

  &.large {
    padding: 40px 30px;
  }

  .stat-label {
    font-size: 32px;
    color: #b8d8e8;
    margin-bottom: 16px;
  }

  .stat-value {
    font-size: 48px;
    font-weight: bold;
    color: #ffc460;
    font-family: DIN, DIN;

    .stat-unit {
      font-size: 32px;
      margin-left: 8px;
      color: #ffffff;
    }
  }
}

.match-charts {
  display: flex;
  gap: 40px;
  margin-bottom: 30px;

  .chart-item {
    flex: 1;
    background: rgba(13, 61, 77, 0.3);
    border: 1px solid rgba(35, 218, 255, 0.3);
    border-radius: 4px;
    padding: 30px;

    .chart-title {
      font-size: 36px;
      font-weight: bold;
      color: #ffffff;
      text-align: center;
      margin-bottom: 20px;
    }

    .chart-container {
      width: 100%;
      height: 500px;
    }
  }
}

.match-table,
.payment-table {
  background: rgba(13, 61, 77, 0.3);
  border: 1px solid rgba(35, 218, 255, 0.3);
  border-radius: 4px;
  padding: 20px;
}

.payment-chart-wrapper {
  background: rgba(13, 61, 77, 0.3);
  border: 1px solid rgba(35, 218, 255, 0.3);
  border-radius: 4px;
  padding: 30px;
  margin-bottom: 30px;

  .chart-title {
    font-size: 36px;
    font-weight: bold;
    color: #ffffff;
    text-align: center;
    margin-bottom: 20px;
  }

  .payment-chart-container {
    width: 100%;
    height: 500px;
  }
}
</style>
